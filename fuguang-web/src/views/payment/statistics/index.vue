<template>
  <div class="app-container">
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日支付总额</span>
          </div>
          <div class="text item">
            <span class="count">¥{{ todayAmount }}</span>
            <span class="desc">较昨日 {{ todayGrowth }}%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日支付笔数</span>
          </div>
          <div class="text item">
            <span class="count">{{ todayCount }}</span>
            <span class="desc">较昨日 {{ todayCountGrowth }}%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>支付成功率</span>
          </div>
          <div class="text item">
            <span class="count">{{ successRate }}%</span>
            <span class="desc">较昨日 {{ successRateGrowth }}%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>待处理异常</span>
          </div>
          <div class="text item">
            <span class="count exception">{{ exceptionCount }}</span>
            <span class="desc">需要处理</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb8">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>支付趋势分析</span>
            <el-button-group style="float: right;">
              <el-button size="mini" @click="changeTrendPeriod('7d')"
                :type="trendPeriod === '7d' ? 'primary' : ''">7天</el-button>
              <el-button size="mini" @click="changeTrendPeriod('30d')"
                :type="trendPeriod === '30d' ? 'primary' : ''">30天</el-button>
              <el-button size="mini" @click="changeTrendPeriod('90d')"
                :type="trendPeriod === '90d' ? 'primary' : ''">90天</el-button>
            </el-button-group>
          </div>
          <div ref="trendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>支付方式分布</span>
          </div>
          <div ref="channelChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb8">
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>任务支付统计</span>
          </div>
          <div class="payment-stats">
            <div class="stat-item">
              <span class="label">今日支付：</span>
              <span class="value">¥{{ taskPayment.todayAmount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">本月支付：</span>
              <span class="value">¥{{ taskPayment.monthAmount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">待支付订单：</span>
              <span class="value pending">{{ taskPayment.pendingCount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">成功率：</span>
              <span class="value">{{ taskPayment.successRate }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>商城支付统计</span>
          </div>
          <div class="payment-stats">
            <div class="stat-item">
              <span class="label">今日支付：</span>
              <span class="value">¥{{ mallPayment.todayAmount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">本月支付：</span>
              <span class="value">¥{{ mallPayment.monthAmount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">待支付订单：</span>
              <span class="value pending">{{ mallPayment.pendingCount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">成功率：</span>
              <span class="value">{{ mallPayment.successRate }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>线下支付统计</span>
          </div>
          <div class="payment-stats">
            <div class="stat-item">
              <span class="label">今日支付：</span>
              <span class="value">¥{{ offlinePayment.todayAmount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">本月支付：</span>
              <span class="value">¥{{ offlinePayment.monthAmount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">待转账订单：</span>
              <span class="value pending">{{ offlinePayment.pendingTransfer }}</span>
            </div>
            <div class="stat-item">
              <span class="label">转账成功率：</span>
              <span class="value">{{ offlinePayment.transferRate }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>最近支付异常</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="viewAllExceptions">查看全部</el-button>
          </div>
          <el-table :data="recentExceptions" style="width: 100%">
            <el-table-column prop="orderNo" label="订单号" width="180"></el-table-column>
            <el-table-column prop="payType" label="支付方式" width="100">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.pay_type" :value="scope.row.payType" />
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="支付金额" width="120">
              <template slot-scope="scope">
                <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="errorMsg" label="异常信息" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="发生时间" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleException(scope.row)">处理</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getPaymentOverview, getPaymentTrend, getPaymentChannelDistribution, getBusinessTypeStatistics, getPaymentExceptionStatistics } from '@/api/payment/statistics'

export default {
  name: "PaymentStatistics",
  dicts: ['pay_type'],
  data() {
    return {
      // 今日统计
      todayAmount: 0,
      todayCount: 0,
      todayGrowth: 0,
      todayCountGrowth: 0,
      successRate: 0,
      successRateGrowth: 0,
      exceptionCount: 0,

      // 趋势分析
      trendPeriod: '7d',
      trendChart: null,
      channelChart: null,

      // 各类支付统计
      taskPayment: {
        todayAmount: 0,
        monthAmount: 0,
        pendingCount: 0,
        successRate: 0
      },
      mallPayment: {
        todayAmount: 0,
        monthAmount: 0,
        pendingCount: 0,
        successRate: 0
      },
      offlinePayment: {
        todayAmount: 0,
        monthAmount: 0,
        pendingTransfer: 0,
        transferRate: 0
      },

      // 最近异常
      recentExceptions: []
    };
  },
  mounted() {
    this.initCharts();
    this.loadStatistics();
  },
  beforeDestroy() {
    if (this.trendChart) {
      this.trendChart.dispose();
    }
    if (this.channelChart) {
      this.channelChart.dispose();
    }
  },
  methods: {
    initCharts() {
      // 初始化趋势图表
      this.trendChart = echarts.init(this.$refs.trendChart);

      // 初始化渠道分布图表
      this.channelChart = echarts.init(this.$refs.channelChart);

      this.updateTrendChart();
      this.updateChannelChart();
    },

    updateTrendChart() {
      // 获取趋势数据
      getPaymentTrend({ period: this.trendPeriod }).then(response => {
        const data = response.data;
        const dates = [];
        const amounts = [];
        const counts = [];

        if (data && data.length > 0) {
          data.forEach(item => {
            const date = new Date(item.date);
            dates.push((date.getMonth() + 1).toString().padStart(2, '0') + '-' + date.getDate().toString().padStart(2, '0'));
            amounts.push(item.amount || 0);
            counts.push(item.count || 0);
          });
        } else {
          // 默认数据
          for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            dates.push((date.getMonth() + 1).toString().padStart(2, '0') + '-' + date.getDate().toString().padStart(2, '0'));
            amounts.push(0);
            counts.push(0);
          }
        }

        const option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['支付金额', '支付笔数']
          },
          xAxis: {
            type: 'category',
            data: dates
          },
          yAxis: [
            {
              type: 'value',
              name: '金额(元)',
              position: 'left'
            },
            {
              type: 'value',
              name: '笔数',
              position: 'right'
            }
          ],
          series: [
            {
              name: '支付金额',
              type: 'line',
              data: amounts
            },
            {
              name: '支付笔数',
              type: 'bar',
              yAxisIndex: 1,
              data: counts
            }
          ]
        };
        this.trendChart.setOption(option);
      }).catch(error => {
        console.error('获取支付趋势数据失败:', error);
        // 使用默认数据
        const option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['支付金额', '支付笔数']
          },
          xAxis: {
            type: 'category',
            data: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07']
          },
          yAxis: [
            {
              type: 'value',
              name: '金额(元)',
              position: 'left'
            },
            {
              type: 'value',
              name: '笔数',
              position: 'right'
            }
          ],
          series: [
            {
              name: '支付金额',
              type: 'line',
              data: [0, 0, 0, 0, 0, 0, 0]
            },
            {
              name: '支付笔数',
              type: 'bar',
              yAxisIndex: 1,
              data: [0, 0, 0, 0, 0, 0, 0]
            }
          ]
        };
        this.trendChart.setOption(option);
      });
    },

    updateChannelChart() {
      // 获取渠道分布数据
      getPaymentChannelDistribution().then(response => {
        console.log('支付渠道分布原始数据:', response.data);
        const data = response.data;
        const chartData = [];

        if (data && data.length > 0) {
          data.forEach(item => {
            chartData.push({
              // 修复字段映射：后端返回的是 count 和 payTypeName，不是 value 和 name
              value: item.count || 0,
              name: item.payTypeName || '未知'
            });
          });
        } else {
          // 默认数据
          chartData.push(
            { value: 0, name: '支付宝' },
            { value: 0, name: '微信支付' },
            { value: 0, name: '余额支付' }
          );
        }

        console.log('处理后的图表数据:', chartData);

        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [
            {
              name: '支付方式',
              type: 'pie',
              radius: '50%',
              data: chartData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };
        this.channelChart.setOption(option);
      }).catch(error => {
        console.error('获取支付渠道分布失败:', error);
        // 使用默认数据
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [
            {
              name: '支付方式',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 0, name: '支付宝' },
                { value: 0, name: '微信支付' },
                { value: 0, name: '余额支付' }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };
        this.channelChart.setOption(option);
      });
    },

    changeTrendPeriod(period) {
      this.trendPeriod = period;
      this.updateTrendChart();
    },

    loadStatistics() {
      // 加载支付统计概览
      this.getPaymentOverview();
      // 加载各业务类型统计
      this.getBusinessTypeStatistics();
      // 加载异常统计
      this.getPaymentExceptionStatistics();
    },

    // 获取支付统计概览
    getPaymentOverview() {
      console.log('开始获取支付统计概览数据');
      getPaymentOverview().then(response => {
        console.log('支付统计概览原始数据:', response.data);
        const data = response.data;

        // 添加详细的字段映射日志
        console.log('字段映射前 - todayAmount:', data.todayAmount);
        console.log('字段映射前 - todayCount:', data.todayCount);
        console.log('字段映射前 - successRate:', data.successRate);
        console.log('字段映射前 - todayGrowth:', data.todayGrowth);
        console.log('字段映射前 - todayCountGrowth:', data.todayCountGrowth);
        console.log('字段映射前 - successRateGrowth:', data.successRateGrowth);

        this.todayAmount = data.todayAmount || 0;
        this.todayCount = data.todayCount || 0;
        this.todayGrowth = data.todayGrowth || 0;
        this.todayCountGrowth = data.todayCountGrowth || 0;
        this.successRate = data.successRate || 0;
        this.successRateGrowth = data.successRateGrowth || 0;

        console.log('字段映射后 - 页面数据:', {
          todayAmount: this.todayAmount,
          todayCount: this.todayCount,
          todayGrowth: this.todayGrowth,
          todayCountGrowth: this.todayCountGrowth,
          successRate: this.successRate,
          successRateGrowth: this.successRateGrowth
        });
      }).catch(error => {
        console.error('获取支付统计概览失败:', error);
        this.$modal.msgError("获取支付统计概览失败");
      });
    },

    // 获取各业务类型统计
    getBusinessTypeStatistics() {
      console.log('开始获取各业务类型统计数据');
      getBusinessTypeStatistics().then(response => {
        console.log('业务类型统计原始数据:', response.data);
        const data = response.data;
        if (data && data.length > 0) {
          data.forEach(item => {
            console.log('处理业务类型:', item.businessType, '数据:', item);

            if (item.businessType === 'task') {
              console.log('任务支付字段映射 - todayAmount:', item.todayAmount, 'monthAmount:', item.monthAmount);
              this.taskPayment = {
                todayAmount: item.todayAmount || 0,
                monthAmount: item.monthAmount || 0,
                pendingCount: item.pendingCount || 0,
                successRate: item.successRate || 0
              };
              console.log('任务支付映射后数据:', this.taskPayment);
            } else if (item.businessType === 'mall') {
              console.log('商城支付字段映射 - todayAmount:', item.todayAmount, 'monthAmount:', item.monthAmount);
              this.mallPayment = {
                todayAmount: item.todayAmount || 0,
                monthAmount: item.monthAmount || 0,
                pendingCount: item.pendingCount || 0,
                successRate: item.successRate || 0
              };
              console.log('商城支付映射后数据:', this.mallPayment);
            } else if (item.businessType === 'offline') {
              console.log('线下支付字段映射 - todayAmount:', item.todayAmount, 'monthAmount:', item.monthAmount);
              this.offlinePayment = {
                todayAmount: item.todayAmount || 0,
                monthAmount: item.monthAmount || 0,
                pendingTransfer: item.pendingTransfer || 0,
                transferRate: item.transferRate || 0
              };
              console.log('线下支付映射后数据:', this.offlinePayment);
            }
          });
        } else {
          console.log('业务类型统计数据为空或无效');
        }
      }).catch(error => {
        console.error('获取业务类型统计失败:', error);
        this.$modal.msgError("获取业务类型统计失败");
      });
    },

    // 获取支付异常统计
    getPaymentExceptionStatistics() {
      getPaymentExceptionStatistics().then(response => {
        const data = response.data;
        this.exceptionCount = data.totalExceptions || 0;
        this.recentExceptions = data.exceptionTypes || [];
      }).catch(error => {
        console.error('获取支付异常统计失败:', error);
        this.$modal.msgError("获取支付异常统计失败");
      });
    },

    handleException(row) {
      this.$router.push('/payment/callback?orderNo=' + row.orderNo);
    },

    viewAllExceptions() {
      this.$router.push('/payment/callback');
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.count {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  display: block;
}

.count.exception {
  color: #F56C6C;
}

.desc {
  font-size: 12px;
  color: #909399;
  display: block;
  margin-top: 5px;
}

.payment-stats {
  padding: 10px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item .label {
  color: #606266;
  font-size: 14px;
}

.stat-item .value {
  font-weight: bold;
  color: #409EFF;
}

.stat-item .value.pending {
  color: #E6A23C;
}
</style>
