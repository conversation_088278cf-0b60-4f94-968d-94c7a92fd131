<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="类型名称" prop="typeName">
        <el-input v-model="queryParams.typeName" placeholder="请输入类型名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="父类型" prop="parentId">
        <el-select v-model="queryParams.parentId" placeholder="请选择父类型" clearable>
          <el-option label="全部" value="" />
          <el-option label="一级类型" value="0" />
          <el-option v-for="type in firstLevelTypes" :key="type.typeId" :label="type.typeName" :value="type.typeId" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['fuguang:taskType:add']">新增</el-button>
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">{{ isExpandAll ? '折叠' :
          '展开' }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" border :data="taskTypeList" row-key="typeId"
      :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange">
      <el-table-column label="类型名称" align="left" prop="typeName" :show-overflow-tooltip="true" min-width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.typeName }}</span>
          <el-tag v-if="scope.row.parentId === 0" type="primary" size="mini" style="margin-left: 8px;">一级</el-tag>
          <el-tag v-else type="success" size="mini" style="margin-left: 8px;">二级</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="类型图标" align="center" prop="typeIcon" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.typeIcon" :width="50" :height="50" v-if="scope.row.typeIcon" />
          <span v-else class="text-muted">无图标</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="orderNum" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="子类型数量" align="center" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.parentId === 0">
            <el-tag type="info" size="mini">{{ getChildrenCount(scope.row.typeId) }}</el-tag>
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAddChild(scope.row)"
            v-if="scope.row.parentId === 0" v-hasPermi="['fuguang:taskType:add']">添加子类型</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['fuguang:taskType:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:taskType:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>


    <!-- 添加或修改任务类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="form.typeName" placeholder="请输入类型名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="父级类型" prop="parentId">
          <el-select v-model="form.parentId" placeholder="请选择父类型" style="width: 100%;" :disabled="isChildTypeFixed">
            <el-option label="一级类型" :value="0" />
            <el-option v-for="type in firstLevelTypes" :key="type.typeId" :label="type.typeName" :value="type.typeId" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="0" :max="999" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="类型状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="类型图标" prop="typeIcon">
          <image-upload v-model="form.typeIcon" :limit="1" />
          <div class="el-upload__tip" style="margin-top: 5px;">
            建议上传尺寸为64x64像素的PNG或JPG图片，文件大小不超过2MB
          </div>
        </el-form-item>
        <el-form-item label="类型描述" prop="typeDesc">
          <el-input v-model="form.typeDesc" type="textarea" placeholder="请输入类型描述信息" :rows="3" maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaskType, getTaskType, delTaskType, addTaskType, updateTaskType, getFirstLevelTypes } from "@/api/fuguang/taskType";

export default {
  name: "TaskType",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 任务类型表格数据
      taskTypeList: [],
      // 一级类型列表
      firstLevelTypes: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 提交按钮loading状态
      submitLoading: false,
      // 是否固定子类型（添加子类型时使用）
      isChildTypeFixed: false,
      // 查询参数
      queryParams: {
        typeName: null,
        parentId: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        typeName: [
          { required: true, message: "类型名称不能为空", trigger: "blur" },
          { min: 2, max: 50, message: "类型名称长度在2到50个字符", trigger: "blur" }
        ],
        parentId: [
          { required: true, message: "父类型不能为空", trigger: "change" }
        ],
        orderNum: [
          { required: true, message: "排序不能为空", trigger: "blur" },
          { type: 'number', min: 0, max: 999, message: "排序必须在0-999之间", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getFirstLevelTypes();
  },
  methods: {
    /** 查询任务类型列表 */
    getList() {
      this.loading = true;
      listTaskType(this.queryParams).then(response => {
        // 构建树形结构数据
        const data = response.rows || [];
        this.taskTypeList = this.handleTree(data, "typeId", "parentId");
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取任务类型列表失败:', error);
        this.taskTypeList = [];
        this.loading = false;
        this.$modal.msgError("获取任务类型列表失败");
      });
    },
    /** 获取一级类型列表 */
    getFirstLevelTypes() {
      getFirstLevelTypes().then(response => {
        this.firstLevelTypes = response.data || [];
      }).catch(error => {
        console.error('获取一级类型列表失败:', error);
        this.firstLevelTypes = [];
      });
    },
    /** 获取父类型名称 */
    getParentTypeName(parentId) {
      const parent = this.firstLevelTypes.find(item => item.typeId === parentId);
      return parent ? parent.typeName : '';
    },
    /** 获取子类型数量 */
    getChildrenCount(parentId) {
      const flatList = this.flattenTree(this.taskTypeList);
      return flatList.filter(item => item.parentId === parentId).length;
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 扁平化树形数据 */
    flattenTree(tree) {
      let result = [];
      tree.forEach(node => {
        result.push(node);
        if (node.children && node.children.length > 0) {
          result = result.concat(this.flattenTree(node.children));
        }
      });
      return result;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        typeId: null,
        typeName: null,
        parentId: 0,
        typeIcon: null,
        orderNum: 0,
        status: "0",
        typeDesc: null
      };
      this.resetForm("form");
      this.submitLoading = false;
      this.isChildTypeFixed = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.typeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加任务类型";
    },
    /** 添加子类型操作 */
    handleAddChild(row) {
      this.reset();
      this.form.parentId = row.typeId;
      this.isChildTypeFixed = true;
      this.open = true;
      this.title = "添加子类型 - " + row.typeName;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const typeId = row.typeId || this.ids
      getTaskType(typeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改任务类型";
      }).catch(error => {
        console.error('获取任务类型详情失败:', error);
        this.$modal.msgError("获取任务类型详情失败");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitLoading = true;
          if (this.form.typeId != null) {
            updateTaskType(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.getFirstLevelTypes(); // 刷新一级类型列表
            }).catch(error => {
              console.error('修改任务类型失败:', error);
              this.$modal.msgError("修改失败");
            }).finally(() => {
              this.submitLoading = false;
            });
          } else {
            addTaskType(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.getFirstLevelTypes(); // 刷新一级类型列表
            }).catch(error => {
              console.error('新增任务类型失败:', error);
              this.$modal.msgError("新增失败");
            }).finally(() => {
              this.submitLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const typeIds = row.typeId || this.ids;
      const typeName = row.typeName || '选中';

      // 检查是否有子类型
      if (row.parentId === 0) {
        const childrenCount = this.getChildrenCount(row.typeId);
        if (childrenCount > 0) {
          this.$modal.msgWarning(`该类型下还有${childrenCount}个子类型，请先删除子类型`);
          return;
        }
      }

      this.$modal.confirm(`是否确认删除任务类型"${typeName}"？`).then(function () {
        return delTaskType(typeIds);
      }).then(() => {
        this.getList();
        this.getFirstLevelTypes(); // 刷新一级类型列表
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/taskType/export', {
        ...this.queryParams
      }, `taskType_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.text-muted {
  color: #909399;
  font-size: 12px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}

/* 树形表格样式优化 */
.el-table .el-table__row .el-table__cell {
  border-bottom: 1px solid #ebeef5;
}

.el-table .el-table__row:hover .el-table__cell {
  background-color: #f5f7fa;
}

/* 标签样式 */
.el-tag {
  margin-left: 8px;
}

/* 对话框表单样式 */
.el-dialog .el-form .el-form-item {
  margin-bottom: 18px;
}

.el-dialog .el-form .el-form-item:last-child {
  margin-bottom: 0;
}
</style>
