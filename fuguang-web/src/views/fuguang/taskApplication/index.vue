<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务ID" prop="taskId">
        <el-input v-model="queryParams.taskId" placeholder="请输入任务ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="申请人" prop="applicantName">
        <el-input v-model="queryParams.applicantName" placeholder="请输入申请人昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="申请状态" prop="applicationStatus">
        <el-select v-model="queryParams.applicationStatus" placeholder="请选择申请状态" clearable>
          <el-option label="待确认" value="0" />
          <el-option label="已确认" value="1" />
          <el-option label="已拒绝" value="2" />
          <el-option label="已取消" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['fuguang:taskApplication:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['fuguang:taskApplication:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['fuguang:taskApplication:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['fuguang:taskApplication:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="taskApplicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请ID" align="center" prop="applicationId" width="80" />
      <el-table-column label="任务ID" align="center" prop="taskId" width="80" />
      <el-table-column label="申请人" align="center" prop="applicantName" width="120" />
      <el-table-column label="申请理由" align="center" prop="applicationReason" :show-overflow-tooltip="true" />
      <el-table-column label="申请状态" align="center" prop="applicationStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.applicationStatus === '0'" type="warning">待确认</el-tag>
          <el-tag v-else-if="scope.row.applicationStatus === '1'" type="success">已确认</el-tag>
          <el-tag v-else-if="scope.row.applicationStatus === '2'" type="danger">已拒绝</el-tag>
          <el-tag v-else-if="scope.row.applicationStatus === '3'" type="info">已取消</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="确认时间" align="center" prop="confirmTime" width="180">
        <template slot-scope="scope">
          <span v-if="scope.row.confirmTime">{{ parseTime(scope.row.confirmTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['fuguang:taskApplication:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['fuguang:taskApplication:remove']">删除</el-button>
          <el-button v-if="scope.row.applicationStatus === '0'" size="mini" type="text" icon="el-icon-check" @click="handleConfirm(scope.row)" v-hasPermi="['fuguang:taskApplication:confirm']">确认</el-button>
          <el-button v-if="scope.row.applicationStatus === '0'" size="mini" type="text" icon="el-icon-close" @click="handleReject(scope.row)" v-hasPermi="['fuguang:taskApplication:reject']">拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改APP任务申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="任务ID" prop="taskId">
          <el-input v-model="form.taskId" placeholder="请输入任务ID" />
        </el-form-item>
        <el-form-item label="申请人ID" prop="applicantId">
          <el-input v-model="form.applicantId" placeholder="请输入申请人ID" />
        </el-form-item>
        <el-form-item label="申请人昵称" prop="applicantName">
          <el-input v-model="form.applicantName" placeholder="请输入申请人昵称" />
        </el-form-item>
        <el-form-item label="申请理由" prop="applicationReason">
          <el-input v-model="form.applicationReason" type="textarea" placeholder="请输入申请理由" />
        </el-form-item>
        <el-form-item label="申请状态" prop="applicationStatus">
          <el-select v-model="form.applicationStatus" placeholder="请选择申请状态">
            <el-option label="待确认" value="0" />
            <el-option label="已确认" value="1" />
            <el-option label="已拒绝" value="2" />
            <el-option label="已取消" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaskApplication, getTaskApplication, delTaskApplication, addTaskApplication, updateTaskApplication, adminConfirmApplication, adminRejectApplication } from "@/api/fuguang/taskApplication";

export default {
  name: "TaskApplication",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // APP任务申请表格数据
      taskApplicationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskId: null,
        applicantName: null,
        applicationStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskId: [
          { required: true, message: "任务ID不能为空", trigger: "blur" }
        ],
        applicantId: [
          { required: true, message: "申请人ID不能为空", trigger: "blur" }
        ],
        applicantName: [
          { required: true, message: "申请人昵称不能为空", trigger: "blur" }
        ],
        applicationStatus: [
          { required: true, message: "申请状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询APP任务申请列表 */
    getList() {
      this.loading = true;
      listTaskApplication(this.queryParams).then(response => {
        this.taskApplicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        applicationId: null,
        taskId: null,
        applicantId: null,
        applicantName: null,
        applicantAvatar: null,
        applicationReason: null,
        applicationStatus: "0",
        confirmTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加APP任务申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const applicationId = row.applicationId || this.ids
      getTaskApplication(applicationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改APP任务申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.applicationId != null) {
            updateTaskApplication(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTaskApplication(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const applicationIds = row.applicationId || this.ids;
      this.$modal.confirm('是否确认删除APP任务申请编号为"' + applicationIds + '"的数据项？').then(function() {
        return delTaskApplication(applicationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/taskApplication/export', {
        ...this.queryParams
      }, `taskApplication_${new Date().getTime()}.xlsx`)
    },
    /** 确认申请 */
    handleConfirm(row) {
      this.$modal.confirm('是否确认该申请？确认后任务将分配给申请人。').then(function() {
        return adminConfirmApplication(row.applicationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("确认成功");
      }).catch(() => {});
    },
    /** 拒绝申请 */
    handleReject(row) {
      this.$modal.confirm('是否确认拒绝该申请？').then(function() {
        return adminRejectApplication(row.applicationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("拒绝成功");
      }).catch(() => {});
    }
  }
};
</script>
