<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商家名称" prop="merchantName">
        <el-input v-model="queryParams.merchantName" placeholder="请输入商家名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['fuguang:merchantQrcode:add']">新增</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['fuguang:merchantQrcode:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="merchantQrcodeList" @selection-change="handleSelectionChange">
      <el-table-column label="二维码ID" align="center" prop="qrcodeId" width="100" />
      <el-table-column label="商家ID" align="center" prop="merchantId" width="100" />
      <el-table-column label="商家名称" align="center" prop="merchantName" width="150" />
      <el-table-column label="二维码内容" align="center" prop="qrcodeContent" width="300" show-overflow-tooltip />
      <el-table-column label="二维码图片" align="center" prop="qrcodeUrl" width="120">
        <template slot-scope="scope">
          <el-image v-if="scope.row.qrcodeUrl" :src="scope.row.qrcodeUrl" style="width: 60px; height: 60px;"
            fit="contain" :preview-src-list="[scope.row.qrcodeUrl]" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-hasPermi="['fuguang:merchantQrcode:query']">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['fuguang:merchantQrcode:edit']">修改</el-button>
          <el-button size="mini" type="text" :icon="scope.row.status === '0' ? 'el-icon-close' : 'el-icon-check'"
            @click="handleStatusChange(scope.row)" v-hasPermi="['fuguang:merchantQrcode:edit']"
            :style="{ color: scope.row.status === '0' ? '#F56C6C' : '#67C23A' }">{{ scope.row.status === '0' ? '禁用' :
              '启用' }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:merchantQrcode:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改商家二维码对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商家ID" prop="merchantId">
          <el-input v-model="form.merchantId" placeholder="请输入商家ID" />
        </el-form-item>
        <el-form-item label="商家名称" prop="merchantName">
          <el-input v-model="form.merchantName" placeholder="请输入商家名称" />
        </el-form-item>
        <el-form-item label="二维码内容" prop="qrcodeContent">
          <el-input v-model="form.qrcodeContent" type="textarea" placeholder="请输入二维码内容" />
        </el-form-item>
        <el-form-item label="二维码图片" prop="qrcodeUrl">
          <el-input v-model="form.qrcodeUrl" placeholder="请输入二维码图片URL" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{ dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="二维码详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-form ref="viewForm" :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="二维码ID">
              <span>{{ form.qrcodeId }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商家ID">
              <span>{{ form.merchantId }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="商家名称">
              <span>{{ form.merchantName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="二维码内容">
              <el-input v-model="form.qrcodeContent" type="textarea" :rows="3" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态">
              <dict-tag :options="dict.type.sys_normal_disable" :value="form.status" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间">
              <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qrcodeUrl">
          <el-col :span="24">
            <el-form-item label="二维码图片">
              <el-image :src="form.qrcodeUrl" style="width: 200px; height: 200px;" fit="contain"
                :preview-src-list="[form.qrcodeUrl]" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 生成二维码对话框 -->
    <el-dialog title="生成商家二维码" :visible.sync="generateOpen" width="400px" append-to-body>
      <el-form ref="generateForm" :model="generateForm" :rules="generateRules" label-width="80px">
        <el-form-item label="商家ID" prop="merchantId">
          <el-input v-model="generateForm.merchantId" placeholder="请输入商家ID" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGenerate">确 定</el-button>
        <el-button @click="generateOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMerchantQrcode, getMerchantQrcode, delMerchantQrcode, addMerchantQrcode, updateMerchantQrcode, changeStatus, generateQrcode, batchChangeStatus } from "@/api/fuguang/merchantQrcode"

export default {
  name: "MerchantQrcode",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商家二维码表格数据
      merchantQrcodeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 是否显示生成二维码弹出层
      generateOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        merchantName: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 生成二维码表单参数
      generateForm: {},
      // 表单校验
      rules: {
        merchantId: [
          { required: true, message: "商家ID不能为空", trigger: "blur" }
        ],
        merchantName: [
          { required: true, message: "商家名称不能为空", trigger: "blur" }
        ],
        qrcodeContent: [
          { required: true, message: "二维码内容不能为空", trigger: "blur" }
        ],
      },
      // 生成二维码表单校验
      generateRules: {
        merchantId: [
          { required: true, message: "商家ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询商家二维码列表 */
    getList() {
      this.loading = true;
      listMerchantQrcode(this.queryParams).then(response => {
        this.merchantQrcodeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        qrcodeId: null,
        merchantId: null,
        merchantName: null,
        qrcodeContent: null,
        qrcodeUrl: null,
        status: "0",
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.qrcodeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商家二维码";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const qrcodeId = row.qrcodeId || this.ids
      getMerchantQrcode(qrcodeId).then(response => {
        this.form = response.data;
        this.viewOpen = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const qrcodeId = row.qrcodeId || this.ids
      getMerchantQrcode(qrcodeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商家二维码";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.qrcodeId != null) {
            updateMerchantQrcode(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMerchantQrcode(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const qrcodeIds = row.qrcodeId || this.ids;
      this.$modal.confirm('是否确认删除商家二维码编号为"' + qrcodeIds + '"的数据项？').then(function () {
        return delMerchantQrcode(qrcodeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/merchantQrcode/export', {
        ...this.queryParams
      }, `merchantQrcode_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.merchantName + '"的二维码吗？').then(function () {
        return changeStatus(row.qrcodeId, row.status === "0" ? "1" : "0");
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    /** 生成二维码按钮操作 */
    handleGenerate() {
      this.generateForm = { merchantId: null };
      this.generateOpen = true;
    },
    /** 提交生成二维码 */
    submitGenerate() {
      this.$refs["generateForm"].validate(valid => {
        if (valid) {
          generateQrcode(this.generateForm.merchantId).then(response => {
            this.$modal.msgSuccess("二维码生成成功");
            this.generateOpen = false;
            this.getList();
          });
        }
      });
    }
  }
};
</script>
