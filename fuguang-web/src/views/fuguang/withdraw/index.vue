<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>待审核</span>
            <i class="el-icon-time" style="color: #E6A23C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #E6A23C;">{{ pendingInfo.count || 0 }}</div>
            <div class="card-sub">¥{{ pendingInfo.amount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>总提现</span>
            <i class="el-icon-money"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ statistics.totalCount || 0 }}</div>
            <div class="card-sub">¥{{ statistics.totalAmount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>成功提现</span>
            <i class="el-icon-success" style="color: #67C23A;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #67C23A;">{{ statistics.successCount || 0 }}</div>
            <div class="card-sub">¥{{ statistics.successAmount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>成功率</span>
            <i class="el-icon-pie-chart"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ (statistics.successRate || 0).toFixed(1) }}%</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户昵称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="提现状态" prop="withdrawStatus">
        <el-select v-model="queryParams.withdrawStatus" placeholder="请选择提现状态" clearable>
          <el-option label="申请中" value="0" />
          <el-option label="处理中" value="1" />
          <el-option label="提现成功" value="2" />
          <el-option label="提现失败" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="提现方式" prop="withdrawType">
        <el-select v-model="queryParams.withdrawType" placeholder="请选择提现方式" clearable>
          <el-option label="支付宝" value="1" />
          <el-option label="微信" value="2" />
          <el-option label="银行卡" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-check" size="mini" :disabled="multiple"
          @click="handleBatchApprove" v-hasPermi="['fuguang:withdraw:audit']">批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-close" size="mini" :disabled="multiple" @click="handleBatchReject"
          v-hasPermi="['fuguang:withdraw:audit']">批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['fuguang:withdraw:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="withdrawList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="提现单号" align="center" prop="withdrawNo" width="180" />
      <el-table-column label="用户信息" align="center" width="120">
        <template slot-scope="scope">
          <div>{{ scope.row.userName }}</div>
          <div style="color: #909399; font-size: 12px;">ID: {{ scope.row.userId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="提现金额" align="center" prop="withdrawAmount">
        <template slot-scope="scope">
          <span style="color: #F56C6C; font-weight: bold;">¥{{ scope.row.withdrawAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手续费" align="center" prop="withdrawFee">
        <template slot-scope="scope">
          <span>¥{{ scope.row.withdrawFee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际到账" align="center" prop="actualAmount">
        <template slot-scope="scope">
          <span style="color: #67C23A; font-weight: bold;">¥{{ scope.row.actualAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提现方式" align="center" prop="withdrawType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.withdrawType === '1'" type="success">支付宝</el-tag>
          <el-tag v-else-if="scope.row.withdrawType === '2'" type="primary">微信</el-tag>
          <el-tag v-else-if="scope.row.withdrawType === '3'" type="warning">银行卡</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="收款信息" align="center" width="150">
        <template slot-scope="scope">
          <div>{{ scope.row.payeeName }}</div>
          <div style="color: #909399; font-size: 12px;">{{ scope.row.payeeAccount }}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="withdrawStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.withdrawStatus === '0'" type="warning">申请中</el-tag>
          <el-tag v-else-if="scope.row.withdrawStatus === '1'" type="primary">处理中</el-tag>
          <el-tag v-else-if="scope.row.withdrawStatus === '2'" type="success">提现成功</el-tag>
          <el-tag v-else-if="scope.row.withdrawStatus === '3'" type="danger">提现失败</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="applyTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-hasPermi="['fuguang:withdraw:query']">详情</el-button>
          <el-button v-if="scope.row.withdrawStatus === '0'" size="mini" type="text" icon="el-icon-check"
            @click="handleApprove(scope.row)" v-hasPermi="['fuguang:withdraw:audit']">通过</el-button>
          <el-button v-if="scope.row.withdrawStatus === '0'" size="mini" type="text" icon="el-icon-close"
            @click="handleReject(scope.row)" v-hasPermi="['fuguang:withdraw:audit']">拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 查看详情对话框 -->
    <el-dialog title="提现详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="提现单号">{{ viewData.withdrawNo }}</el-descriptions-item>
        <el-descriptions-item label="用户信息">{{ viewData.userName }} (ID: {{ viewData.userId }})</el-descriptions-item>
        <el-descriptions-item label="提现金额">¥{{ viewData.withdrawAmount }}</el-descriptions-item>
        <el-descriptions-item label="手续费">¥{{ viewData.withdrawFee }}</el-descriptions-item>
        <el-descriptions-item label="实际到账">¥{{ viewData.actualAmount }}</el-descriptions-item>
        <el-descriptions-item label="提现方式">{{ viewData.withdrawTypeName }}</el-descriptions-item>
        <el-descriptions-item label="收款人">{{ viewData.payeeName }}</el-descriptions-item>
        <el-descriptions-item label="收款账户">{{ viewData.payeeAccount }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ viewData.withdrawStatusName }}</el-descriptions-item>
        <el-descriptions-item label="第三方交易号">{{ viewData.tradeNo || '无' }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(viewData.applyTime, '{y}-{m}-{d} {h}:{i}:{s}')
        }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">{{ viewData.processTime ? parseTime(viewData.processTime, '{y}-{m}-{d}
          { h }: { i }: { s }') : '未处理' }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ viewData.finishTime ? parseTime(viewData.finishTime, '{y}-{m}-{d}
          { h }: { i }: { s }') : '未完成' }}</el-descriptions-item>
        <el-descriptions-item label="失败原因" v-if="viewData.failReason">{{ viewData.failReason }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 拒绝提现对话框 -->
    <el-dialog title="拒绝提现" :visible.sync="rejectOpen" width="400px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="80px">
        <el-form-item label="提现单号">
          <el-input v-model="rejectForm.withdrawNo" disabled />
        </el-form-item>
        <el-form-item label="提现金额">
          <el-input v-model="rejectForm.withdrawAmount" disabled />
        </el-form-item>
        <el-form-item label="拒绝原因" prop="reason">
          <el-input v-model="rejectForm.reason" type="textarea" placeholder="请输入拒绝原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReject">确 定</el-button>
        <el-button @click="rejectOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量拒绝对话框 -->
    <el-dialog title="批量拒绝提现" :visible.sync="batchRejectOpen" width="400px" append-to-body>
      <el-form ref="batchRejectForm" :model="batchRejectForm" :rules="batchRejectRules" label-width="80px">
        <el-form-item label="选中数量">
          <el-input :value="ids.length + ' 条'" disabled />
        </el-form-item>
        <el-form-item label="拒绝原因" prop="reason">
          <el-input v-model="batchRejectForm.reason" type="textarea" placeholder="请输入拒绝原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchReject">确 定</el-button>
        <el-button @click="batchRejectOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWithdrawRecord, getWithdrawRecord, auditWithdraw, batchAuditWithdraw, getPendingCount, getWithdrawStatistics } from "@/api/fuguang/withdraw";

export default {
  name: "Withdraw",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 提现记录表格数据
      withdrawList: [],
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否显示拒绝弹出层
      rejectOpen: false,
      // 是否显示批量拒绝弹出层
      batchRejectOpen: false,
      // 查看数据
      viewData: {},
      // 待审核信息
      pendingInfo: {},
      // 统计数据
      statistics: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
        withdrawStatus: null,
        withdrawType: null,
      },
      // 拒绝表单参数
      rejectForm: {},
      // 批量拒绝表单参数
      batchRejectForm: {},
      // 拒绝表单校验
      rejectRules: {
        reason: [
          { required: true, message: "拒绝原因不能为空", trigger: "blur" }
        ]
      },
      // 批量拒绝表单校验
      batchRejectRules: {
        reason: [
          { required: true, message: "拒绝原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getPendingInfo();
    this.getStatistics();
  },
  methods: {
    /** 查询提现记录列表 */
    getList() {
      this.loading = true;
      listWithdrawRecord(this.queryParams).then(response => {
        this.withdrawList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取待审核信息 */
    getPendingInfo() {
      getPendingCount().then(response => {
        this.pendingInfo = response.data;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      getWithdrawStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.withdrawId)
      this.multiple = !selection.length
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      getWithdrawRecord(row.withdrawId).then(response => {
        this.viewData = response.data;
        this.viewOpen = true;
      });
    },
    /** 通过审核按钮操作 */
    handleApprove(row) {
      this.$modal.confirm('是否确认通过提现单号为"' + row.withdrawNo + '"的提现申请？').then(() => {
        return auditWithdraw({
          withdrawId: row.withdrawId,
          action: 'approve'
        });
      }).then(() => {
        this.getList();
        this.getPendingInfo();
        this.getStatistics();
        this.$modal.msgSuccess("审核通过成功");
      }).catch(() => { });
    },
    /** 拒绝审核按钮操作 */
    handleReject(row) {
      this.rejectForm = {
        withdrawId: row.withdrawId,
        withdrawNo: row.withdrawNo,
        withdrawAmount: '¥' + row.withdrawAmount,
        reason: ''
      };
      this.rejectOpen = true;
    },
    /** 提交拒绝 */
    submitReject() {
      this.$refs["rejectForm"].validate(valid => {
        if (valid) {
          auditWithdraw({
            withdrawId: this.rejectForm.withdrawId,
            action: 'reject',
            reason: this.rejectForm.reason
          }).then(() => {
            this.$modal.msgSuccess("拒绝成功");
            this.rejectOpen = false;
            this.getList();
            this.getPendingInfo();
            this.getStatistics();
          });
        }
      });
    },
    /** 批量通过按钮操作 */
    handleBatchApprove() {
      this.$modal.confirm('是否确认批量通过选中的 ' + this.ids.length + ' 条提现申请？').then(() => {
        return batchAuditWithdraw({
          withdrawIds: this.ids,
          action: 'approve'
        });
      }).then(() => {
        this.getList();
        this.getPendingInfo();
        this.getStatistics();
        this.$modal.msgSuccess("批量审核通过成功");
      }).catch(() => { });
    },
    /** 批量拒绝按钮操作 */
    handleBatchReject() {
      this.batchRejectForm = {
        reason: ''
      };
      this.batchRejectOpen = true;
    },
    /** 提交批量拒绝 */
    submitBatchReject() {
      this.$refs["batchRejectForm"].validate(valid => {
        if (valid) {
          batchAuditWithdraw({
            withdrawIds: this.ids,
            action: 'reject',
            reason: this.batchRejectForm.reason
          }).then(() => {
            this.$modal.msgSuccess("批量拒绝成功");
            this.batchRejectOpen = false;
            this.getList();
            this.getPendingInfo();
            this.getStatistics();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/withdraw/export', {
        ...this.queryParams
      }, `withdraw_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.box-card {
  height: 100px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #909399;
}

.card-header i {
  font-size: 20px;
}

.card-content {
  margin-top: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-sub {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
