<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="订单号" prop="orderNo">
                <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="订单状态" prop="orderStatus">
                <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable>
                    <el-option label="待付款" value="0" />
                    <el-option label="已付款" value="1" />
                    <el-option label="已发货" value="2" />
                    <el-option label="已完成" value="3" />
                    <el-option label="已取消" value="4" />
                    <el-option label="已退款" value="5" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付状态" prop="payStatus">
                <el-select v-model="queryParams.payStatus" placeholder="请选择支付状态" clearable>
                    <el-option label="未支付" value="0" />
                    <el-option label="已支付" value="1" />
                    <el-option label="支付失败" value="2" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-truck" size="mini" :disabled="multiple"
                    @click="handleBatchDelivery" v-hasPermi="['mall:order:delivery']">批量发货</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-close" size="mini" :disabled="multiple"
                    @click="handleBatchCancel" v-hasPermi="['mall:order:cancel']">批量取消</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="el-icon-money" size="mini" :disabled="multiple"
                    @click="handleBatchRefund" v-hasPermi="['mall:order:refund']">批量退款</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                    v-hasPermi="['mall:order:export']">导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" border :data="orderList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="订单号" align="center" prop="orderNo" />
            <el-table-column label="用户ID" align="center" prop="userId" />
            <el-table-column label="订单金额" align="center" prop="totalAmount" />
            <el-table-column label="实付金额" align="center" prop="payAmount" />
            <el-table-column label="订单状态" align="center" prop="orderStatus">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.mall_order_status" :value="scope.row.orderStatus" />
                </template>
            </el-table-column>
            <el-table-column label="支付状态" align="center" prop="payStatus">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.mall_pay_status" :value="scope.row.payStatus" />
                </template>
            </el-table-column>
            <el-table-column label="收货人" align="center" prop="receiverName" />
            <el-table-column label="收货电话" align="center" prop="receiverPhone" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)"
                        v-hasPermi="['mall:order:detail']">详情</el-button>
                    <el-button size="mini" type="text" icon="el-icon-truck" @click="handleDelivery(scope.row)"
                        v-hasPermi="['mall:order:delivery']" v-if="scope.row.orderStatus == '1'">发货</el-button>
                    <el-button size="mini" type="text" icon="el-icon-close" @click="handleCancel(scope.row)"
                        v-hasPermi="['mall:order:cancel']" v-if="scope.row.orderStatus == '0'">取消</el-button>
                    <el-button size="mini" type="text" icon="el-icon-money" @click="handleRefund(scope.row)"
                        v-hasPermi="['mall:order:refund']"
                        v-if="scope.row.orderStatus == '2' && scope.row.payStatus == '1'">退款</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 订单详情对话框 -->
        <el-dialog title="订单详情" :visible.sync="detailOpen" width="800px" append-to-body>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="订单号">{{ orderDetail.orderNo }}</el-descriptions-item>
                <el-descriptions-item label="订单状态">
                    <dict-tag :options="dict.type.mall_order_status" :value="orderDetail.orderStatus" />
                </el-descriptions-item>
                <el-descriptions-item label="支付状态">
                    <dict-tag :options="dict.type.mall_pay_status" :value="orderDetail.payStatus" />
                </el-descriptions-item>
                <el-descriptions-item label="订单金额">￥{{ orderDetail.totalAmount }}</el-descriptions-item>
                <el-descriptions-item label="实付金额">￥{{ orderDetail.payAmount }}</el-descriptions-item>
                <el-descriptions-item label="配送费">￥{{ orderDetail.deliveryFee || 0 }}</el-descriptions-item>
                <el-descriptions-item label="收货人">{{ orderDetail.receiverName }}</el-descriptions-item>
                <el-descriptions-item label="收货电话">{{ orderDetail.receiverPhone }}</el-descriptions-item>
                <el-descriptions-item label="收货地址" :span="2">{{ orderDetail.receiverAddress }}</el-descriptions-item>
                <el-descriptions-item label="订单备注" :span="2">{{ orderDetail.remark || '无' }}</el-descriptions-item>
                <el-descriptions-item label="创建时间">{{ parseTime(orderDetail.createTime) }}</el-descriptions-item>
                <el-descriptions-item label="支付时间">{{ parseTime(orderDetail.payTime) || '未支付' }}</el-descriptions-item>
                <el-descriptions-item label="发货时间">{{ parseTime(orderDetail.deliveryTime) || '未发货'
                }}</el-descriptions-item>
                <el-descriptions-item label="完成时间">{{ parseTime(orderDetail.finishTime) || '未完成'
                }}</el-descriptions-item>
            </el-descriptions>

            <div style="margin-top: 20px;">
                <h4>订单商品</h4>
                <el-table :data="orderDetail.orderItems" border>
                    <el-table-column label="商品图片" width="80">
                        <template slot-scope="scope">
                            <image-preview :src="scope.row.productImage" :width="50" :height="50" />
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" prop="productName" />
                    <el-table-column label="单价" prop="productPrice" />
                    <el-table-column label="数量" prop="quantity" />
                    <el-table-column label="小计" prop="totalPrice" />
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listOrder, getOrder, deliveryOrder, cancelOrder } from "@/api/mall/order";

export default {
    name: "Order",
    dicts: ['mall_order_status', 'mall_pay_status'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 订单表格数据
            orderList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 是否显示详情弹出层
            detailOpen: false,
            // 订单详情
            orderDetail: {},
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                orderNo: null,
                orderStatus: null,
                payStatus: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询订单列表 */
        getList() {
            this.loading = true;
            listOrder(this.queryParams).then(response => {
                this.orderList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                orderId: null,
                orderNo: null,
                userId: null,
                totalAmount: null,
                payAmount: null,
                orderStatus: null,
                payStatus: null,
                payType: null,
                payTime: null,
                deliveryTime: null,
                finishTime: null,
                cancelTime: null,
                receiverName: null,
                receiverPhone: null,
                receiverAddress: null,
                deliveryFee: null,
                remark: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.orderId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 订单详情按钮操作 */
        handleDetail(row) {
            this.reset();
            const orderId = row.orderId || this.ids
            getOrder(orderId).then(response => {
                this.orderDetail = response.data;
                this.detailOpen = true;
            });
        },
        /** 发货按钮操作 */
        handleDelivery(row) {
            const orderIds = row.orderId || this.ids;
            this.$modal.confirm('是否确认发货订单编号为"' + row.orderNo + '"的数据项？').then(function () {
                return deliveryOrder(orderIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("发货成功");
            }).catch(() => { });
        },
        /** 取消订单按钮操作 */
        handleCancel(row) {
            const orderIds = row.orderId || this.ids;
            this.$modal.confirm('是否确认取消订单编号为"' + row.orderNo + '"的数据项？').then(function () {
                return cancelOrder(orderIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("取消成功");
            }).catch(() => { });
        },
        /** 退款按钮操作 */
        handleRefund(row) {
            const orderIds = row.orderId || this.ids;
            this.$modal.confirm('是否确认退款订单编号为"' + row.orderNo + '"的数据项？').then(function () {
                // 这里需要调用退款接口，暂时使用取消订单接口代替
                return cancelOrder(orderIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("退款成功");
            }).catch(() => { });
        },
        /** 批量发货按钮操作 */
        handleBatchDelivery() {
            const orderIds = this.ids;
            this.$modal.confirm('是否确认批量发货选中的订单？').then(function () {
                return Promise.all(orderIds.map(id => deliveryOrder(id)));
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("批量发货成功");
            }).catch(() => { });
        },
        /** 批量取消按钮操作 */
        handleBatchCancel() {
            const orderIds = this.ids;
            this.$modal.confirm('是否确认批量取消选中的订单？').then(function () {
                return Promise.all(orderIds.map(id => cancelOrder(id)));
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("批量取消成功");
            }).catch(() => { });
        },
        /** 批量退款按钮操作 */
        handleBatchRefund() {
            const orderIds = this.ids;
            this.$modal.confirm('是否确认批量退款选中的订单？').then(function () {
                // 这里需要调用退款接口，暂时使用取消订单接口代替
                return Promise.all(orderIds.map(id => cancelOrder(id)));
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("批量退款成功");
            }).catch(() => { });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('mall/order/export', {
                ...this.queryParams
            }, `order_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>