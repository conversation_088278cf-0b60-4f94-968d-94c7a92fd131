<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单ID" prop="orderId">
        <el-input v-model="queryParams.orderId" placeholder="请输入订单ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="物流公司" prop="logisticsCompany">
        <el-input v-model="queryParams.logisticsCompany" placeholder="请输入物流公司" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="物流状态" prop="logisticsStatus">
        <el-select v-model="queryParams.logisticsStatus" placeholder="请选择物流状态" clearable>
          <el-option v-for="dict in dict.type.mall_logistics_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['mall:logistics:add']">新增</el-button>
        <el-button type="info" plain icon="el-icon-truck" size="mini" @click="handleDelivery">创建发货</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['mall:logistics:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="logisticsList" @selection-change="handleSelectionChange">
      <el-table-column label="物流ID" align="center" prop="logisticsId" />
      <el-table-column label="订单ID" align="center" prop="orderId" />
      <el-table-column label="物流公司" align="center" prop="logisticsCompany" />
      <el-table-column label="物流单号" align="center" prop="trackingNumber" />
      <el-table-column label="物流状态" align="center" prop="logisticsStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.mall_logistics_status" :value="scope.row.logisticsStatus" />
        </template>
      </el-table-column>
      <el-table-column label="发货时间" align="center" prop="shipTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.shipTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="签收时间" align="center" prop="receiveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.receiveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['mall:logistics:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['mall:logistics:remove']">删除</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)"
            v-hasPermi="['mall:logistics:edit']">
            <span class="el-dropdown-link">
              <i class="el-icon-d-arrow-right el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="handleUpdateStatus" icon="el-icon-refresh">更新状态</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改订单物流对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订单ID" prop="orderId">
          <el-input v-model="form.orderId" placeholder="请输入订单ID" />
        </el-form-item>
        <el-form-item label="物流公司" prop="logisticsCompany">
          <el-input v-model="form.logisticsCompany" placeholder="请输入物流公司" />
        </el-form-item>
        <el-form-item label="物流单号" prop="trackingNumber">
          <el-input v-model="form.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="物流状态" prop="logisticsStatus">
          <el-select v-model="form.logisticsStatus" placeholder="请选择物流状态">
            <el-option v-for="dict in dict.type.mall_logistics_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发货时间" prop="shipTime">
          <el-date-picker clearable v-model="form.shipTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择发货时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="签收时间" prop="receiveTime">
          <el-date-picker clearable v-model="form.receiveTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择签收时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 创建发货记录对话框 -->
    <el-dialog title="创建发货记录" :visible.sync="deliveryOpen" width="500px" append-to-body>
      <el-form ref="deliveryForm" :model="deliveryForm" :rules="deliveryRules" label-width="80px">
        <el-form-item label="订单ID" prop="orderId">
          <el-input v-model="deliveryForm.orderId" placeholder="请输入订单ID" />
        </el-form-item>
        <el-form-item label="物流公司" prop="logisticsCompany">
          <el-input v-model="deliveryForm.logisticsCompany" placeholder="请输入物流公司" />
        </el-form-item>
        <el-form-item label="物流单号" prop="trackingNumber">
          <el-input v-model="deliveryForm.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="deliveryForm.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDeliveryForm">确 定</el-button>
        <el-button @click="cancelDelivery">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 更新物流状态对话框 -->
    <el-dialog title="更新物流状态" :visible.sync="statusOpen" width="400px" append-to-body>
      <el-form ref="statusForm" :model="statusForm" :rules="statusRules" label-width="80px">
        <el-form-item label="物流状态" prop="logisticsStatus">
          <el-select v-model="statusForm.logisticsStatus" placeholder="请选择物流状态">
            <el-option v-for="dict in dict.type.mall_logistics_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="statusForm.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitStatusForm">确 定</el-button>
        <el-button @click="cancelStatus">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLogistics, getLogistics, delLogistics, addLogistics, updateLogistics, createDelivery, updateLogisticsStatus } from "@/api/mall/logistics";

export default {
  name: "Logistics",
  dicts: ['mall_logistics_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单物流表格数据
      logisticsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示发货弹出层
      deliveryOpen: false,
      // 是否显示状态更新弹出层
      statusOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderId: null,
        logisticsCompany: null,
        logisticsStatus: null,
      },
      // 表单参数
      form: {},
      // 发货表单参数
      deliveryForm: {},
      // 状态更新表单参数
      statusForm: {},
      // 表单校验
      rules: {
        orderId: [
          { required: true, message: "订单ID不能为空", trigger: "blur" }
        ],
        logisticsCompany: [
          { required: true, message: "物流公司不能为空", trigger: "blur" }
        ],
        trackingNumber: [
          { required: true, message: "物流单号不能为空", trigger: "blur" }
        ],
      },
      // 发货表单校验
      deliveryRules: {
        orderId: [
          { required: true, message: "订单ID不能为空", trigger: "blur" }
        ],
        logisticsCompany: [
          { required: true, message: "物流公司不能为空", trigger: "blur" }
        ],
        trackingNumber: [
          { required: true, message: "物流单号不能为空", trigger: "blur" }
        ],
      },
      // 状态更新表单校验
      statusRules: {
        logisticsStatus: [
          { required: true, message: "物流状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单物流列表 */
    getList() {
      this.loading = true;
      listLogistics(this.queryParams).then(response => {
        this.logisticsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消发货
    cancelDelivery() {
      this.deliveryOpen = false;
      this.resetDelivery();
    },
    // 取消状态更新
    cancelStatus() {
      this.statusOpen = false;
      this.resetStatus();
    },
    // 表单重置
    reset() {
      this.form = {
        logisticsId: null,
        orderId: null,
        logisticsCompany: null,
        trackingNumber: null,
        logisticsStatus: null,
        shipTime: null,
        receiveTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    // 发货表单重置
    resetDelivery() {
      this.deliveryForm = {
        orderId: null,
        logisticsCompany: null,
        trackingNumber: null,
        remark: null
      };
      this.resetForm("deliveryForm");
    },
    // 状态表单重置
    resetStatus() {
      this.statusForm = {
        logisticsId: null,
        logisticsStatus: null,
        remark: null
      };
      this.resetForm("statusForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.logisticsId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加订单物流";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const logisticsId = row.logisticsId || this.ids
      getLogistics(logisticsId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改订单物流";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.logisticsId != null) {
            updateLogistics(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLogistics(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const logisticsIds = row.logisticsId || this.ids;
      this.$modal.confirm('是否确认删除订单物流编号为"' + logisticsIds + '"的数据项？').then(function () {
        return delLogistics(logisticsIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mall/logistics/export', {
        ...this.queryParams
      }, `logistics_${new Date().getTime()}.xlsx`)
    },
    /** 创建发货记录 */
    handleDelivery() {
      this.resetDelivery();
      this.deliveryOpen = true;
    },
    /** 提交发货表单 */
    submitDeliveryForm() {
      this.$refs["deliveryForm"].validate(valid => {
        if (valid) {
          createDelivery(this.deliveryForm).then(response => {
            this.$modal.msgSuccess("发货成功");
            this.deliveryOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "handleUpdateStatus":
          this.handleUpdateStatus(row);
          break;
        default:
          break;
      }
    },
    /** 更新物流状态 */
    handleUpdateStatus(row) {
      this.resetStatus();
      this.statusForm.logisticsId = row.logisticsId;
      this.statusOpen = true;
    },
    /** 提交状态更新表单 */
    submitStatusForm() {
      this.$refs["statusForm"].validate(valid => {
        if (valid) {
          updateLogisticsStatus(this.statusForm).then(response => {
            this.$modal.msgSuccess("状态更新成功");
            this.statusOpen = false;
            this.getList();
          });
        }
      });
    }
  }
};
</script>
