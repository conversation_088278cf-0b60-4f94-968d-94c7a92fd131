import request from "@/utils/request";

// 查询商城支付记录列表
export function listMallPayment(query) {
  return request({
    url: "/mall/payment/list",
    method: "get",
    params: query,
  });
}

// 查询商城支付记录详细
export function getMallPayment(paymentId) {
  return request({
    url: "/mall/payment/" + paymentId,
    method: "get",
  });
}

// 新增商城支付记录
export function addMallPayment(data) {
  return request({
    url: "/mall/payment",
    method: "post",
    data: data,
  });
}

// 修改商城支付记录
export function updateMallPayment(data) {
  return request({
    url: "/mall/payment",
    method: "put",
    data: data,
  });
}

// 删除商城支付记录
export function delMallPayment(paymentId) {
  return request({
    url: "/mall/payment/" + paymentId,
    method: "delete",
  });
}

// 导出商城支付记录
export function exportMallPayment(query) {
  return request({
    url: "/mall/payment/export",
    method: "post",
    params: query,
  });
}

// 根据订单号查询支付记录
export function getMallPaymentByOrderNo(orderNo) {
  return request({
    url: "/mall/payment/orderNo/" + orderNo,
    method: "get",
  });
}

// 根据订单ID查询支付记录
export function getMallPaymentByOrderId(orderId) {
  return request({
    url: "/mall/payment/orderId/" + orderId,
    method: "get",
  });
}

// 获取商城支付统计信息
export function getMallPaymentStatistics() {
  return request({
    url: "/mall/payment/statistics",
    method: "get",
  });
}

// 重新发起支付
export function retryMallPayment(paymentId) {
  return request({
    url: "/mall/payment/retry/" + paymentId,
    method: "post",
  });
}

// 模拟支付宝回调支付成功
export function simulateMallPaymentCallback(paymentId) {
  return request({
    url: "/mall/payment/simulateCallback/" + paymentId,
    method: "post",
  });
}
