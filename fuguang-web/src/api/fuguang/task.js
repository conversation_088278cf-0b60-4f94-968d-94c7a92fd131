import request from "@/utils/request";

// 查询APP任务列表
export function listTask(query) {
  return request({
    url: "/fuguang/task/list",
    method: "get",
    params: query,
  });
}

// 查询APP任务详细
export function getTask(taskId) {
  return request({
    url: "/fuguang/task/" + taskId,
    method: "get",
  });
}

// 新增APP任务
export function addTask(data) {
  return request({
    url: "/fuguang/task",
    method: "post",
    data: data,
  });
}

// 修改APP任务
export function updateTask(data) {
  return request({
    url: "/fuguang/task",
    method: "put",
    data: data,
  });
}

// 删除APP任务
export function delTask(taskId) {
  return request({
    url: "/fuguang/task/" + taskId,
    method: "delete",
  });
}

// 审核任务
export function auditTask(data) {
  return request({
    url: "/fuguang/task/audit",
    method: "put",
    data: data,
  });
}

// 强制完成任务
export function forceCompleteTask(taskId) {
  return request({
    url: "/fuguang/task/complete/" + taskId,
    method: "put",
  });
}

// 强制取消任务
export function forceCancelTask(taskId) {
  return request({
    url: "/fuguang/task/cancel/" + taskId,
    method: "put",
  });
}

// 终止任务
export function terminateTask(taskId) {
  return request({
    url: "/fuguang/task/terminate/" + taskId,
    method: "put",
  });
}

// 获取任务统计数据
export function getTaskStats() {
  return request({
    url: "/fuguang/task/stats",
    method: "get",
  });
}
