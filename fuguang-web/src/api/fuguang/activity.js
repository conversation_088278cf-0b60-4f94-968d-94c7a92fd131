import request from '@/utils/request'

// 查询APP活动管理列表
export function listAppActivity(query) {
  return request({
    url: '/fuguang/activity/list',
    method: 'get',
    params: query
  })
}

// 查询APP活动管理详细
export function getAppActivity(activityId) {
  return request({
    url: '/fuguang/activity/' + activityId,
    method: 'get'
  })
}

// 新增APP活动管理
export function addAppActivity(data) {
  return request({
    url: '/fuguang/activity',
    method: 'post',
    data: data
  })
}

// 修改APP活动管理
export function updateAppActivity(data) {
  return request({
    url: '/fuguang/activity',
    method: 'put',
    data: data
  })
}

// 删除APP活动管理
export function delAppActivity(activityId) {
  return request({
    url: '/fuguang/activity/' + activityId,
    method: 'delete'
  })
}

// 获取启用的APP活动列表
export function getEnabledActivities() {
  return request({
    url: '/fuguang/activity/enabled',
    method: 'get'
  })
}

// 获取当前有效的活动列表
export function getValidActivities() {
  return request({
    url: '/fuguang/activity/valid',
    method: 'get'
  })
}

// 修改活动状态
export function changeStatus(data) {
  return request({
    url: '/fuguang/activity/changeStatus',
    method: 'put',
    data: data
  })
}
