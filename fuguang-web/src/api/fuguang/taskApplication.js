import request from "@/utils/request";

// 查询APP任务申请列表
export function listTaskApplication(query) {
  return request({
    url: "/fuguang/taskApplication/list",
    method: "get",
    params: query,
  });
}

// 查询APP任务申请详细
export function getTaskApplication(applicationId) {
  return request({
    url: "/fuguang/taskApplication/" + applicationId,
    method: "get",
  });
}

// 新增APP任务申请
export function addTaskApplication(data) {
  return request({
    url: "/fuguang/taskApplication",
    method: "post",
    data: data,
  });
}

// 修改APP任务申请
export function updateTaskApplication(data) {
  return request({
    url: "/fuguang/taskApplication",
    method: "put",
    data: data,
  });
}

// 删除APP任务申请
export function delTaskApplication(applicationId) {
  return request({
    url: "/fuguang/taskApplication/" + applicationId,
    method: "delete",
  });
}

// 根据任务ID查询申请列表
export function getApplicationsByTaskId(taskId) {
  return request({
    url: "/fuguang/taskApplication/task/" + taskId,
    method: "get",
  });
}

// 管理员确认申请
export function adminConfirmApplication(applicationId) {
  return request({
    url: "/fuguang/taskApplication/confirm/" + applicationId,
    method: "put",
  });
}

// 管理员拒绝申请
export function adminRejectApplication(applicationId) {
  return request({
    url: "/fuguang/taskApplication/reject/" + applicationId,
    method: "put",
  });
}

// 导出APP任务申请
export function exportTaskApplication(query) {
  return request({
    url: "/fuguang/taskApplication/export",
    method: "post",
    params: query,
  });
}
