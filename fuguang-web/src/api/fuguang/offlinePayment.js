import request from "@/utils/request";

// 查询线下支付订单列表
export function listOfflinePayment(query) {
  return request({
    url: "/fuguang/offlinePayment/list",
    method: "get",
    params: query,
  });
}

// 查询线下支付订单详细
export function getOfflinePayment(paymentId) {
  return request({
    url: "/fuguang/offlinePayment/" + paymentId,
    method: "get",
  });
}

// 新增线下支付订单
export function addOfflinePayment(data) {
  return request({
    url: "/fuguang/offlinePayment",
    method: "post",
    data: data,
  });
}

// 修改线下支付订单
export function updateOfflinePayment(data) {
  return request({
    url: "/fuguang/offlinePayment",
    method: "put",
    data: data,
  });
}

// 删除线下支付订单
export function delOfflinePayment(paymentId) {
  return request({
    url: "/fuguang/offlinePayment/" + paymentId,
    method: "delete",
  });
}

// 重新转账给商家
export function retryTransfer(paymentId) {
  return request({
    url: "/fuguang/offlinePayment/transfer/" + paymentId,
    method: "post",
  });
}

// 模拟支付宝回调支付成功
export function simulateOfflinePaymentCallback(paymentId) {
  return request({
    url: "/fuguang/offlinePayment/simulateCallback/" + paymentId,
    method: "post",
  });
}

// 根据订单号查询支付订单
export function getOfflinePaymentByOrderNo(orderNo) {
  return request({
    url: "/fuguang/offlinePayment/orderNo/" + orderNo,
    method: "get",
  });
}

// 获取支付统计信息
export function getOfflinePaymentStatistics() {
  return request({
    url: "/fuguang/offlinePayment/statistics",
    method: "get",
  });
}
