import request from '@/utils/request'

// 查询支付回调日志列表
export function listPaymentCallback(query) {
  return request({
    url: '/payment/callback/list',
    method: 'get',
    params: query
  })
}

// 查询支付回调日志详细
export function getPaymentCallback(callbackId) {
  return request({
    url: '/payment/callback/' + callbackId,
    method: 'get'
  })
}

// 删除支付回调日志
export function delPaymentCallback(callbackId) {
  return request({
    url: '/payment/callback/' + callbackId,
    method: 'delete'
  })
}

// 批量删除支付回调日志
export function delPaymentCallbacks(callbackIds) {
  return request({
    url: '/payment/callback/' + callbackIds,
    method: 'delete'
  })
}

// 重试支付回调
export function retryPaymentCallback(callbackId) {
  return request({
    url: '/payment/callback/retry/' + callbackId,
    method: 'post'
  })
}

// 批量重试支付回调
export function retryPaymentCallbacks(callbackIds) {
  return request({
    url: '/payment/callback/retry',
    method: 'post',
    data: { callbackIds }
  })
}

// 导出支付回调日志
export function exportPaymentCallback(query) {
  return request({
    url: '/payment/callback/export',
    method: 'post',
    params: query
  })
}

// 获取回调统计信息
export function getCallbackStatistics() {
  return request({
    url: '/payment/callback/statistics',
    method: 'get'
  })
}
