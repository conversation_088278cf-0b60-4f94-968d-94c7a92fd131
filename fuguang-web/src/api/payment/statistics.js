import request from '@/utils/request'

// 获取支付统计概览
export function getPaymentOverview() {
  return request({
    url: '/payment/statistics/overview',
    method: 'get'
  })
}

// 获取支付趋势数据
export function getPaymentTrend(params) {
  return request({
    url: '/payment/statistics/trend',
    method: 'get',
    params
  })
}

// 获取支付渠道分布
export function getPaymentChannelDistribution() {
  return request({
    url: '/payment/statistics/channel',
    method: 'get'
  })
}

// 获取各业务类型支付统计
export function getBusinessTypeStatistics() {
  return request({
    url: '/payment/statistics/businessType',
    method: 'get'
  })
}

// 获取支付异常统计
export function getPaymentExceptionStatistics() {
  return request({
    url: '/payment/statistics/exception',
    method: 'get'
  })
}

// 获取最近支付异常列表
export function getRecentExceptions(params) {
  return request({
    url: '/payment/statistics/recentExceptions',
    method: 'get',
    params
  })
}
