import request from '@/utils/request'

// 获取支付宝配置
export function getAlipayConfig() {
  return request({
    url: '/payment/config/alipay',
    method: 'get'
  })
}

// 保存支付宝配置
export function saveAlipayConfig(data) {
  return request({
    url: '/payment/config/alipay',
    method: 'post',
    data: data
  })
}

// 测试支付宝连接
export function testAlipayConnection() {
  return request({
    url: '/payment/config/alipay/test',
    method: 'post'
  })
}

// 获取微信支付配置
export function getWechatConfig() {
  return request({
    url: '/payment/config/wechat',
    method: 'get'
  })
}

// 保存微信支付配置
export function saveWechatConfig(data) {
  return request({
    url: '/payment/config/wechat',
    method: 'post',
    data: data
  })
}

// 测试微信支付连接
export function testWechatConnection() {
  return request({
    url: '/payment/config/wechat/test',
    method: 'post'
  })
}

// 获取系统配置
export function getSystemConfig() {
  return request({
    url: '/payment/config/system',
    method: 'get'
  })
}

// 保存系统配置
export function saveSystemConfig(data) {
  return request({
    url: '/payment/config/system',
    method: 'post',
    data: data
  })
}
