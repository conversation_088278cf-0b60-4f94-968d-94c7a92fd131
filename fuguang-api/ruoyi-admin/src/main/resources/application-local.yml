
# 支付宝配置
alipay:
  # 应用ID（请替换为实际的应用ID）
  appId: 2021005185670626
  # 商户私钥（请替换为实际的私钥）
  privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3XCHTvafIaJN37pzjn3d+NTbql9K3P/sNdyTp/U1APCOSxgl9U6wooAKPYeB/euH7uqeJ5BoYH8zWpgu6f2aEfLSZqCliJxriEUC2nnetiluAM91+PbevXxsNwhfbtWzlOTQ743avvq2MdGbrwIv24NEAu9zSVxl21gVR3D++2vzilGEL8dj1D8SdhwMIA/jWSPaoRLc5IU/E3bYbiSOt09UZsAUAhQh0ywcbic2HTH25NXb5fliUa8COwJDtfeJc0r7LXGdxYNhMaxvPgM4e4RSowj70ExwoWZq01YM6pvrihIc8x7eyIOn68sHGrdO04EJfemvVu22qCV/to0KNAgMBAAECggEAdByeuzRFdI5snCz5IteP1tch8Iq2AB8+/rbtJFOoQL4UlNRdnASJZ6x0EpIblP6e7kaz44kT0SUKFP9PPAmDRPCneQpZMnOq/W+JeFt/U+DispulblTs5dwnCJ5Zd2M7EykXX7laJk5sHh50MmRa/bo9/+X3HyfS00MSWVVM9LEpHhrMdQggtnhs0T/7BjBo08rfUgMw/l3Mjwx48tU9NBul/bTurCeibbldsHirgfTLTT43ftbMCA/vHf2GmyEpOzJx7iKXWLtT/Db90u/9cDjfb022mya8mqkspHBSPpdBko5rzXO6j9u46hetBxNi2IFDj50PfbN/w7EkT5MlbQKBgQD4ys7T/MUtWVSC6ObPRt47vHr/EOgpJKsIOLvpt4D5O2fXxTVjlnCUKF/6o8WIVQ5pyR2GVuDNs7FfWBzIen3GvL2JdSCgrgSV/7Eg69nRV9lfhEcUYE+itMZ1jqJ4OMPFKC9B3gjo9UXs/IP662N18gHvkg/Pxi5GzWy0/Z4EbwKBgQC8rAn03UFS+z577qqstLWAkvAQ6eySUHPE9FfJDTvAwmwf6ivOh8qzJbyRw7evx0KHq+jbc8BVcmqgMc5MiboJkvIlSfeeDhaBzSSb42Ngx8GOGDrBT+hdHF2TZT+EiLdR3q2W99hKJ2o6n/CJgxveTstsNIT/d8/WPOpkIX0+wwKBgCtyl34T1Yde41BdRfmKwcKi3sPueuy8d5Xe+ooNPtvHkRHwYat7mTt2dZR6x0NQv0ygD6TnRSkHKYGiDJzCL3bmfR8lMkZ9PVXIqnE6XvadJdv1aMhZLW8XrNTYzOy71Qx4QRB6qwmr08NYMeA+/UaatdXVpyc8z9YTh9lvtQnHAoGBALV1JRJLJgOgPmVFkwMNvi7No3Qw92V1WRLJChEE2D44/3LmboFxWoNYPPdYbDb0Bsmjjg5aUlYb9+7gWBCGudVxbdBtRmjOFdl7KsV/Odof5Mk8Bm5b4xiCKoGTdDaMovtrljrHXk9bfzCpGNe4sDnsQHtuO6fUXKEo7ymkh+evAoGAb9+663EqBUstqGnXTHB5efY7XniOqcRkH75WT9yaOxdg/akrzrFKulgB/9OoZEqq60iBqMXPljTcDkYYM9DsOkIFN59ehouSBydvWh0AABqk0g3Cor9XDBcU7Q1Rv7NS1ij2f0gTI8bCWf5KJGcDu7TQCC/y4xrXc4MW+0tseFU=
  # 支付宝公钥（请替换为实际的公钥）
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt1wh072nyGiTd+6c4593fjU26pfStz/7DXck6f1NQDwjksYJfVOsKKACj2Hgf3rh+7qnieQaGB/M1qYLun9mhHy0magpYica4hFAtp53rYpbgDPdfj23r18bDcIX27Vs5Tk0O+N2r76tjHRm68CL9uDRALvc0lcZdtYFUdw/vtr84pRhC/HY9Q/EnYcDCAP41kj2qES3OSFPxN22G4kjrdPVGbAFAIUIdMsHG4nNh0x9uTV2+X5YlGvAjsCQ7X3iXNK+y1xncWDYTGsbz4DOHuEUqMI+9BMcKFmatNWDOqb64oSHPMe3siDp+vLBxq3TtOBCX3pr1bttqglf7aNCjQIDAQAB
  # 服务器异步通知页面路径
  notifyUrl: https://web.hbfgbl.com/payment/alipay/notify
  # 页面跳转同步通知页面路径
  returnUrl: http://your-domain.com/app/payment/alipay/return
  # 签名方式
  signType: RSA2
  # 字符编码格式
  charset: UTF-8
  # 支付宝网关（沙箱环境）
  gatewayUrl: https://openapi.alipaydev.com/gateway.do
  # 是否使用证书模式（true=证书模式，false=公钥模式）
  useCert: true
  # 应用公钥证书路径（证书模式时必填）
  appCertPath: /Users/<USER>/浮光壁垒/支付证书/appCertPublicKey_2021005185670626.crt
  # 支付宝公钥证书路径（证书模式时必填）
  alipayCertPath: /Users/<USER>/浮光壁垒/支付证书/alipayCertPublicKey_RSA2.crt
  # 支付宝根证书路径（证书模式时必填）
  alipayRootCertPath: /Users/<USER>/浮光壁垒/支付证书/alipayRootCert.crt
# 数据源配置
spring:
  # redis 配置
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: fgbl!2025
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: jdbc:mysql://*************:3306/fuguang?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
        username: fuguang
        password: d2KRcr8mP4EaNRJ7
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
