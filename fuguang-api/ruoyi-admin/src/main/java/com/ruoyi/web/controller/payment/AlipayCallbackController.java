package com.ruoyi.web.controller.payment;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.fuguang.service.IAlipayService;
import com.ruoyi.fuguang.service.ITaskPaymentService;
import com.ruoyi.fuguang.service.IMallPaymentService;
import com.ruoyi.fuguang.service.IOfflinePaymentService;
import com.ruoyi.fuguang.service.IPaymentCallbackLogService;
import com.ruoyi.fuguang.domain.TaskPayment;
import com.ruoyi.fuguang.domain.MallPayment;
import com.ruoyi.fuguang.domain.OfflinePayment;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 支付宝统一回调处理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/payment/alipay")
public class AlipayCallbackController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(AlipayCallbackController.class);

    @Autowired
    private IAlipayService alipayService;

    @Autowired
    private ITaskPaymentService taskPaymentService;

    @Autowired
    private IMallPaymentService mallPaymentService;

    @Autowired
    private IOfflinePaymentService offlinePaymentService;

    @Autowired
    private IPaymentCallbackLogService paymentCallbackLogService;

    /**
     * 支付宝统一支付回调
     */
    @Anonymous
    @PostMapping("/notify")
    public String alipayNotify(HttpServletRequest request)
    {
        log.info("收到支付宝支付回调通知");
        
        try {
            // 获取支付宝回调参数
            Map<String, String> params = new HashMap<>();
            Enumeration<String> parameterNames = request.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String paramName = parameterNames.nextElement();
                String paramValue = request.getParameter(paramName);
                params.put(paramName, paramValue);
            }

            log.info("支付宝回调参数：{}", params);

            // 验证签名
            if (!alipayService.verifyCallback(params)) {
                log.error("支付宝回调签名验证失败");

                // 记录签名验证失败的回调日志
                String outTradeNo = params.get("out_trade_no");
                if (outTradeNo != null && !outTradeNo.trim().isEmpty()) {
                    try {
                        String totalAmount = params.get("total_amount");
                        java.math.BigDecimal payAmount = java.math.BigDecimal.ZERO;
                        if (totalAmount != null && !totalAmount.isEmpty()) {
                            payAmount = new java.math.BigDecimal(totalAmount);
                        }

                        String businessType = "unknown";
                        if (outTradeNo.startsWith("TASK")) {
                            businessType = "task";
                        } else if (outTradeNo.startsWith("MALL")) {
                            businessType = "mall";
                        } else if (outTradeNo.startsWith("OFF")) {
                            businessType = "offline";
                        }

                        paymentCallbackLogService.logPaymentCallback(outTradeNo, businessType, "1",
                            payAmount, params.toString(), false, "签名验证失败");
                    } catch (Exception e) {
                        log.error("记录签名验证失败日志异常", e);
                    }
                }

                return "failure";
            }

            // 获取订单号
            String outTradeNo = params.get("out_trade_no");
            if (outTradeNo == null || outTradeNo.trim().isEmpty()) {
                log.error("支付宝回调订单号为空");

                // 记录订单号为空的回调日志
                try {
                    paymentCallbackLogService.logPaymentCallback("", "unknown", "1",
                        java.math.BigDecimal.ZERO, params.toString(), false, "订单号为空");
                } catch (Exception e) {
                    log.error("记录订单号为空日志异常", e);
                }

                return "failure";
            }

            log.info("处理支付宝回调，订单号：{}", outTradeNo);

            // 根据订单号前缀路由到不同的支付处理逻辑
            boolean result = false;
            String businessType = "";
            java.math.BigDecimal payAmount = java.math.BigDecimal.ZERO;
            String errorMsg = null;

            try {
                // 获取支付金额
                String totalAmount = params.get("total_amount");
                if (totalAmount != null && !totalAmount.isEmpty()) {
                    payAmount = new java.math.BigDecimal(totalAmount);
                }

                if (outTradeNo.startsWith("TASK")) {
                    // 任务支付
                    businessType = "task";
                    log.info("处理任务支付回调，订单号：{}", outTradeNo);
                    result = taskPaymentService.handleAlipayCallback(params);
                } else if (outTradeNo.startsWith("MALL")) {
                    // 商城支付
                    businessType = "mall";
                    log.info("处理商城支付回调，订单号：{}", outTradeNo);
                    result = mallPaymentService.handleAlipayCallback(params);
                } else if (outTradeNo.startsWith("OFF")) {
                    // 线下支付
                    businessType = "offline";
                    log.info("处理线下支付回调，订单号：{}", outTradeNo);
                    result = offlinePaymentService.handleAlipayCallback(params);
                } else {
                    errorMsg = "未知的订单号前缀：" + outTradeNo;
                    log.error(errorMsg);

                    // 记录回调日志
                    paymentCallbackLogService.logPaymentCallback(outTradeNo, "unknown", "1",
                        payAmount, params.toString(), false, errorMsg);

                    return "failure";
                }
            } catch (Exception e) {
                errorMsg = "处理回调异常：" + e.getMessage();
                log.error("处理支付回调异常，订单号：{}", outTradeNo, e);
                result = false;
            }

            // 记录回调日志
            try {
                paymentCallbackLogService.logPaymentCallback(outTradeNo, businessType, "1",
                    payAmount, params.toString(), result, errorMsg);
            } catch (Exception e) {
                log.error("记录回调日志异常", e);
            }

            if (result) {
                log.info("支付宝回调处理成功，订单号：{}", outTradeNo);
                return "success";
            } else {
                log.error("支付宝回调处理失败，订单号：{}", outTradeNo);
                return "failure";
            }

        } catch (Exception e) {
            log.error("处理支付宝回调异常", e);
            return "failure";
        }
    }

    /**
     * 支付宝同步跳转回调
     */
    @Anonymous
    @PostMapping("/return")
    public String alipayReturn(HttpServletRequest request)
    {
        log.info("收到支付宝同步跳转回调");
        
        try {
            // 获取支付宝回调参数
            Map<String, String> params = new HashMap<>();
            Enumeration<String> parameterNames = request.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String paramName = parameterNames.nextElement();
                String paramValue = request.getParameter(paramName);
                params.put(paramName, paramValue);
            }

            log.info("支付宝同步回调参数：{}", params);

            // 验证签名
            if (!alipayService.verifyCallback(params)) {
                log.error("支付宝同步回调签名验证失败");
                return "支付验证失败";
            }

            String outTradeNo = params.get("out_trade_no");
            String tradeStatus = params.get("trade_status");
            
            log.info("支付宝同步回调，订单号：{}，交易状态：{}", outTradeNo, tradeStatus);

            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                return "支付成功";
            } else {
                return "支付失败";
            }

        } catch (Exception e) {
            log.error("处理支付宝同步回调异常", e);
            return "支付处理异常";
        }
    }
}
