package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppTaskType;
import com.ruoyi.fuguang.service.IAppTaskTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP任务类型管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@RestController("appTaskTypeManageController")
@RequestMapping("/fuguang/taskType")
public class AppTaskTypeManageController extends BaseController
{
    @Autowired
    private IAppTaskTypeService appTaskTypeService;

    /**
     * 查询APP任务类型列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskType:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppTaskType appTaskType)
    {
        List<AppTaskType> list = appTaskTypeService.selectAppTaskTypeList(appTaskType);
        return getDataTable(list);
    }

    /**
     * 导出APP任务类型列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskType:export')")
    @Log(title = "APP任务类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppTaskType appTaskType)
    {
        List<AppTaskType> list = appTaskTypeService.selectAppTaskTypeList(appTaskType);
        ExcelUtil<AppTaskType> util = new ExcelUtil<AppTaskType>(AppTaskType.class);
        util.exportExcel(response, list, "APP任务类型数据");
    }

    /**
     * 获取APP任务类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskType:query')")
    @GetMapping(value = "/{typeId}")
    public AjaxResult getInfo(@PathVariable("typeId") Long typeId)
    {
        return success(appTaskTypeService.selectAppTaskTypeByTypeId(typeId));
    }

    /**
     * 新增APP任务类型
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskType:add')")
    @Log(title = "APP任务类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppTaskType appTaskType)
    {
        return toAjax(appTaskTypeService.insertAppTaskType(appTaskType));
    }

    /**
     * 修改APP任务类型
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskType:edit')")
    @Log(title = "APP任务类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppTaskType appTaskType)
    {
        return toAjax(appTaskTypeService.updateAppTaskType(appTaskType));
    }

    /**
     * 删除APP任务类型
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskType:remove')")
    @Log(title = "APP任务类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{typeIds}")
    public AjaxResult remove(@PathVariable Long[] typeIds)
    {
        return toAjax(appTaskTypeService.deleteAppTaskTypeByTypeIds(typeIds));
    }

    /**
     * 获取一级任务类型列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskType:list')")
    @GetMapping("/firstLevel")
    public AjaxResult getFirstLevelTypes()
    {
        List<AppTaskType> list = appTaskTypeService.selectFirstLevelTaskTypes();
        return success(list);
    }

    /**
     * 根据父类型ID获取子类型列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskType:list')")
    @GetMapping("/children/{parentId}")
    public AjaxResult getChildrenTypes(@PathVariable("parentId") Long parentId)
    {
        List<AppTaskType> list = appTaskTypeService.selectTaskTypesByParentId(parentId);
        return success(list);
    }
}
