package com.ruoyi.web.controller.mall;

import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.MallPayment;
import com.ruoyi.fuguang.service.IMallPaymentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商城支付记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/mall/payment")
public class MallPaymentController extends BaseController
{
    @Autowired
    private IMallPaymentService mallPaymentService;

    /**
     * 查询商城支付记录列表
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:list')")
    @GetMapping("/list")
    public TableDataInfo list(MallPayment mallPayment)
    {
        startPage();
        List<MallPayment> list = mallPaymentService.selectMallPaymentList(mallPayment);
        return getDataTable(list);
    }

    /**
     * 导出商城支付记录列表
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:export')")
    @Log(title = "商城支付记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MallPayment mallPayment)
    {
        List<MallPayment> list = mallPaymentService.selectMallPaymentList(mallPayment);
        ExcelUtil<MallPayment> util = new ExcelUtil<MallPayment>(MallPayment.class);
        util.exportExcel(response, list, "商城支付记录数据");
    }

    /**
     * 获取商城支付记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:query')")
    @GetMapping(value = "/{paymentId}")
    public AjaxResult getInfo(@PathVariable("paymentId") Long paymentId)
    {
        return success(mallPaymentService.selectMallPaymentByPaymentId(paymentId));
    }

    /**
     * 新增商城支付记录
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:add')")
    @Log(title = "商城支付记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MallPayment mallPayment)
    {
        return toAjax(mallPaymentService.insertMallPayment(mallPayment));
    }

    /**
     * 修改商城支付记录
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:edit')")
    @Log(title = "商城支付记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MallPayment mallPayment)
    {
        return toAjax(mallPaymentService.updateMallPayment(mallPayment));
    }

    /**
     * 删除商城支付记录
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:remove')")
    @Log(title = "商城支付记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{paymentIds}")
    public AjaxResult remove(@PathVariable Long[] paymentIds)
    {
        return toAjax(mallPaymentService.deleteMallPaymentByPaymentIds(paymentIds));
    }

    /**
     * 根据订单号查询支付记录
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:query')")
    @GetMapping("/orderNo/{orderNo}")
    public AjaxResult getByOrderNo(@PathVariable String orderNo)
    {
        return success(mallPaymentService.selectPaymentByOrderNo(orderNo));
    }

    /**
     * 模拟支付宝回调支付成功
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:edit')")
    @Log(title = "模拟支付回调", businessType = BusinessType.UPDATE)
    @PostMapping("/simulateCallback/{paymentId}")
    public AjaxResult simulatePaymentCallback(@PathVariable Long paymentId)
    {
        try {
            MallPayment payment = mallPaymentService.selectMallPaymentByPaymentId(paymentId);
            if (payment == null) {
                return error("支付记录不存在");
            }
            if (!"0".equals(payment.getPayStatus())) {
                return error("订单状态不正确，无法模拟回调");
            }

            // 模拟支付宝回调成功
            String mockTradeNo = "MOCK_" + System.currentTimeMillis();
            int result = mallPaymentService.paymentSuccess(payment.getOrderNo(), mockTradeNo, payment.getPayType());

            if (result > 0) {
                return success("模拟支付回调成功");
            } else {
                return error("模拟支付回调失败");
            }
        } catch (Exception e) {
            return error("模拟支付回调异常：" + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询支付记录
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:query')")
    @GetMapping("/orderId/{orderId}")
    public AjaxResult getByOrderId(@PathVariable Long orderId)
    {
        return success(mallPaymentService.selectPaymentByOrderId(orderId));
    }

    /**
     * 获取支付统计信息
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        // 查询各种状态的订单数量和金额
        MallPayment query = new MallPayment();
        
        // 待支付订单
        query.setPayStatus("0");
        List<MallPayment> pendingList = mallPaymentService.selectMallPaymentList(query);
        int pendingCount = pendingList.size();
        BigDecimal pendingAmount = pendingList.stream()
            .map(MallPayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 支付成功订单
        query.setPayStatus("1");
        List<MallPayment> successList = mallPaymentService.selectMallPaymentList(query);
        int successCount = successList.size();
        BigDecimal successAmount = successList.stream()
            .map(MallPayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 支付失败订单
        query.setPayStatus("2");
        List<MallPayment> failedList = mallPaymentService.selectMallPaymentList(query);
        int failedCount = failedList.size();
        BigDecimal failedAmount = failedList.stream()
            .map(MallPayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 构建统计结果
        final BigDecimal finalPendingAmount = pendingAmount;
        final BigDecimal finalSuccessAmount = successAmount;
        final BigDecimal finalFailedAmount = failedAmount;

        return success(new Object() {
            public final int pendingPayments = pendingCount;
            public final BigDecimal pendingAmount = finalPendingAmount;
            public final int successPayments = successCount;
            public final BigDecimal successAmount = finalSuccessAmount;
            public final int failedPayments = failedCount;
            public final BigDecimal failedAmount = finalFailedAmount;
            public final int totalPayments = pendingCount + successCount + failedCount;
            public final BigDecimal totalAmount = finalPendingAmount.add(finalSuccessAmount).add(finalFailedAmount);
        });
    }

    /**
     * 重新发起支付
     */
    @PreAuthorize("@ss.hasPermi('mall:payment:edit')")
    @Log(title = "重新发起支付", businessType = BusinessType.UPDATE)
    @PostMapping("/retry/{paymentId}")
    public AjaxResult retryPayment(@PathVariable Long paymentId)
    {
        try {
            MallPayment payment = mallPaymentService.selectMallPaymentByPaymentId(paymentId);
            if (payment == null) {
                return error("支付记录不存在");
            }
            
            if (!"0".equals(payment.getPayStatus()) && !"2".equals(payment.getPayStatus())) {
                return error("只能重新发起待支付或支付失败的订单");
            }
            
            // 重置支付状态为待支付
            payment.setPayStatus("0");
            payment.setUpdateTime(new java.util.Date());
            mallPaymentService.updateMallPayment(payment);
            
            return success("重新发起支付成功");
        } catch (Exception e) {
            return error("重新发起支付失败：" + e.getMessage());
        }
    }
}
