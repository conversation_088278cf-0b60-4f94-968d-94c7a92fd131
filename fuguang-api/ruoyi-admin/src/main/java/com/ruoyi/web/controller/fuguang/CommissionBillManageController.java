package com.ruoyi.web.controller.fuguang;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.CommissionBill;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 佣金账单管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController("commissionBillManageController")
@RequestMapping("/fuguang/commission")
public class CommissionBillManageController extends BaseController
{
    @Autowired
    private ICommissionBillService commissionBillService;

    /**
     * 查询佣金账单列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:commission:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommissionBill commissionBill)
    {
        startPage();
        List<CommissionBill> list = commissionBillService.selectCommissionBillList(commissionBill);
        return getDataTable(list);
    }

    /**
     * 导出佣金账单列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:commission:export')")
    @Log(title = "佣金账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommissionBill commissionBill)
    {
        List<CommissionBill> list = commissionBillService.selectCommissionBillList(commissionBill);
        ExcelUtil<CommissionBill> util = new ExcelUtil<CommissionBill>(CommissionBill.class);
        util.exportExcel(response, list, "佣金账单数据");
    }

    /**
     * 获取佣金账单详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:commission:query')")
    @GetMapping(value = "/{billId}")
    public AjaxResult getInfo(@PathVariable("billId") Long billId)
    {
        return success(commissionBillService.selectCommissionBillByBillId(billId));
    }


    /**
     * 获取平台佣金统计概览
     */
    @PreAuthorize("@ss.hasPermi('fuguang:commission:statistics')")
    @GetMapping("/overview")
    public AjaxResult getCommissionOverview(@RequestParam(required = false) Integer year,
                                           @RequestParam(required = false) Integer month)
    {
        // 如果没有指定年月，使用当前年月
        if (year == null || month == null) {
            Calendar calendar = Calendar.getInstance();
            year = calendar.get(Calendar.YEAR);
            month = calendar.get(Calendar.MONTH) + 1;
        }

        CommissionBill queryBill = new CommissionBill();
        queryBill.setBillYear(year);
        queryBill.setBillMonth(month);
        List<CommissionBill> bills = commissionBillService.selectCommissionBillList(queryBill);

        Map<String, Object> overview = new HashMap<>();
        BigDecimal totalIncome = BigDecimal.ZERO;
        BigDecimal totalTaskCommission = BigDecimal.ZERO;
        BigDecimal totalRecommendReward = BigDecimal.ZERO;
        BigDecimal totalOtherIncome = BigDecimal.ZERO;
        BigDecimal totalExpense = BigDecimal.ZERO;
        BigDecimal totalWithdraw = BigDecimal.ZERO;
        int totalWithdrawCount = 0;
        int activeUserCount = bills.size();

        for (CommissionBill bill : bills) {
            totalIncome = totalIncome.add(bill.getTotalIncome());
            totalTaskCommission = totalTaskCommission.add(bill.getTaskCommission());
            totalRecommendReward = totalRecommendReward.add(bill.getRecommendReward());
            totalOtherIncome = totalOtherIncome.add(bill.getOtherIncome());
            // 修复总支出统计
            if (bill.getTotalExpense() != null) {
                totalExpense = totalExpense.add(bill.getTotalExpense());
            }
            totalWithdraw = totalWithdraw.add(bill.getTotalWithdraw());
            totalWithdrawCount += bill.getWithdrawCount();
        }

        overview.put("year", year);
        overview.put("month", month);
        overview.put("activeUserCount", activeUserCount);
        overview.put("totalIncome", totalIncome);
        overview.put("totalTaskCommission", totalTaskCommission);
        overview.put("totalRecommendReward", totalRecommendReward);
        overview.put("totalOtherIncome", totalOtherIncome);
        overview.put("totalExpense", totalExpense);
        overview.put("totalWithdraw", totalWithdraw);
        overview.put("totalWithdrawCount", totalWithdrawCount);
        // 取消净收入字段

        return success(overview);
    }

    /**
     * 获取佣金趋势统计（最近12个月）
     */
    @PreAuthorize("@ss.hasPermi('fuguang:commission:statistics')")
    @GetMapping("/trend")
    public AjaxResult getCommissionTrend()
    {
        Calendar calendar = Calendar.getInstance();
        Map<String, Object> trendData = new HashMap<>();
        
        // 获取最近12个月的数据
        for (int i = 11; i >= 0; i--) {
            calendar.add(Calendar.MONTH, -i);
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            
            CommissionBill queryBill = new CommissionBill();
            queryBill.setBillYear(year);
            queryBill.setBillMonth(month);
            List<CommissionBill> bills = commissionBillService.selectCommissionBillList(queryBill);
            
            BigDecimal monthlyIncome = BigDecimal.ZERO;
            BigDecimal monthlyWithdraw = BigDecimal.ZERO;
            
            for (CommissionBill bill : bills) {
                monthlyIncome = monthlyIncome.add(bill.getTotalIncome());
                monthlyWithdraw = monthlyWithdraw.add(bill.getTotalWithdraw());
            }
            
            String monthKey = String.format("%d-%02d", year, month);
            Map<String, Object> monthData = new HashMap<>();
            monthData.put("income", monthlyIncome);
            monthData.put("withdraw", monthlyWithdraw);
            monthData.put("net", monthlyIncome.subtract(monthlyWithdraw));
            monthData.put("userCount", bills.size());
            
            trendData.put(monthKey, monthData);
            
            // 重置日历到当前时间
            calendar = Calendar.getInstance();
        }
        
        return success(trendData);
    }

    /**
     * 获取收入类型分布统计
     */
    @PreAuthorize("@ss.hasPermi('fuguang:commission:statistics')")
    @GetMapping("/income-distribution")
    public AjaxResult getIncomeDistribution(@RequestParam(required = false) Integer year,
                                           @RequestParam(required = false) Integer month)
    {
        // 如果没有指定年月，使用当前年月
        if (year == null || month == null) {
            Calendar calendar = Calendar.getInstance();
            year = calendar.get(Calendar.YEAR);
            month = calendar.get(Calendar.MONTH) + 1;
        }

        CommissionBill queryBill = new CommissionBill();
        queryBill.setBillYear(year);
        queryBill.setBillMonth(month);
        List<CommissionBill> bills = commissionBillService.selectCommissionBillList(queryBill);

        BigDecimal taskCommissionTotal = BigDecimal.ZERO;
        BigDecimal recommendRewardTotal = BigDecimal.ZERO;
        BigDecimal otherIncomeTotal = BigDecimal.ZERO;

        for (CommissionBill bill : bills) {
            taskCommissionTotal = taskCommissionTotal.add(bill.getTaskCommission());
            recommendRewardTotal = recommendRewardTotal.add(bill.getRecommendReward());
            otherIncomeTotal = otherIncomeTotal.add(bill.getOtherIncome());
        }

        Map<String, Object> distribution = new HashMap<>();
        distribution.put("year", year);
        distribution.put("month", month);
        distribution.put("taskCommission", taskCommissionTotal);
        distribution.put("recommendReward", recommendRewardTotal);
        distribution.put("otherIncome", otherIncomeTotal);
        distribution.put("total", taskCommissionTotal.add(recommendRewardTotal).add(otherIncomeTotal));

        return success(distribution);
    }
}
