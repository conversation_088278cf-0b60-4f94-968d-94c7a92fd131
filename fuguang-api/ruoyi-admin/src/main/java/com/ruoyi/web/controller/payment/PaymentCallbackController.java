package com.ruoyi.web.controller.payment;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.PaymentCallbackLog;
import com.ruoyi.fuguang.service.IPaymentCallbackLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 支付回调日志Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/payment/callback")
public class PaymentCallbackController extends BaseController
{
    @Autowired
    private IPaymentCallbackLogService paymentCallbackLogService;

    /**
     * 查询支付回调日志列表
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaymentCallbackLog paymentCallbackLog)
    {
        startPage();
        List<PaymentCallbackLog> list = paymentCallbackLogService.selectPaymentCallbackLogList(paymentCallbackLog);
        return getDataTable(list);
    }

    /**
     * 导出支付回调日志列表
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:export')")
    @Log(title = "支付回调日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaymentCallbackLog paymentCallbackLog)
    {
        List<PaymentCallbackLog> list = paymentCallbackLogService.selectPaymentCallbackLogList(paymentCallbackLog);
        ExcelUtil<PaymentCallbackLog> util = new ExcelUtil<PaymentCallbackLog>(PaymentCallbackLog.class);
        util.exportExcel(response, list, "支付回调日志数据");
    }

    /**
     * 获取支付回调日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:query')")
    @GetMapping(value = "/{callbackId}")
    public AjaxResult getInfo(@PathVariable("callbackId") Long callbackId)
    {
        return success(paymentCallbackLogService.selectPaymentCallbackLogByCallbackId(callbackId));
    }

    /**
     * 新增支付回调日志
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:add')")
    @Log(title = "支付回调日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaymentCallbackLog paymentCallbackLog)
    {
        return toAjax(paymentCallbackLogService.insertPaymentCallbackLog(paymentCallbackLog));
    }

    /**
     * 修改支付回调日志
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:edit')")
    @Log(title = "支付回调日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaymentCallbackLog paymentCallbackLog)
    {
        return toAjax(paymentCallbackLogService.updatePaymentCallbackLog(paymentCallbackLog));
    }

    /**
     * 删除支付回调日志
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:remove')")
    @Log(title = "支付回调日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{callbackIds}")
    public AjaxResult remove(@PathVariable Long[] callbackIds)
    {
        return toAjax(paymentCallbackLogService.deletePaymentCallbackLogByCallbackIds(callbackIds));
    }

    /**
     * 根据订单号查询回调日志
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:query')")
    @GetMapping("/orderNo/{orderNo}")
    public AjaxResult getByOrderNo(@PathVariable String orderNo)
    {
        return success(paymentCallbackLogService.selectPaymentCallbackLogByOrderNo(orderNo));
    }

    /**
     * 重试回调
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:retry')")
    @Log(title = "支付回调重试", businessType = BusinessType.UPDATE)
    @PostMapping("/retry/{callbackId}")
    public AjaxResult retry(@PathVariable Long callbackId)
    {
        boolean result = paymentCallbackLogService.retryCallback(callbackId);
        return result ? success("重试成功") : error("重试失败");
    }

    /**
     * 批量重试回调
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:retry')")
    @Log(title = "支付回调批量重试", businessType = BusinessType.UPDATE)
    @PostMapping("/batchRetry")
    public AjaxResult batchRetry(@RequestBody Long[] callbackIds)
    {
        boolean result = paymentCallbackLogService.batchRetryCallback(callbackIds);
        return result ? success("批量重试成功") : error("批量重试失败");
    }

    /**
     * 获取回调统计信息
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Map<String, Object> statistics = paymentCallbackLogService.getCallbackStatistics();
        return success(statistics);
    }

    /**
     * 获取今日回调统计
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:list')")
    @GetMapping("/todayStatistics")
    public AjaxResult getTodayStatistics()
    {
        Map<String, Object> todayStatistics = paymentCallbackLogService.getTodayCallbackStatistics();
        return success(todayStatistics);
    }

    /**
     * 获取回调成功率统计（按业务类型）
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:list')")
    @GetMapping("/successRateByBusinessType")
    public AjaxResult getSuccessRateByBusinessType()
    {
        List<Map<String, Object>> successRateStats = paymentCallbackLogService.getCallbackSuccessRateByBusinessType();
        return success(successRateStats);
    }

    /**
     * 获取回调趋势统计（最近7天）
     */
    @PreAuthorize("@ss.hasPermi('payment:callback:list')")
    @GetMapping("/trendStatistics")
    public AjaxResult getTrendStatistics()
    {
        List<Map<String, Object>> trendStats = paymentCallbackLogService.getCallbackTrendStatistics();
        return success(trendStats);
    }
}
