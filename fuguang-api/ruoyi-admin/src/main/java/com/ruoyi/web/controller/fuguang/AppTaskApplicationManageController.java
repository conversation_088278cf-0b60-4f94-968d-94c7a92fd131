package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppTaskApplication;
import com.ruoyi.fuguang.service.IAppTaskApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP任务申请管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController("appTaskApplicationManageController")
@RequestMapping("/fuguang/taskApplication")
public class AppTaskApplicationManageController extends BaseController
{
    @Autowired
    private IAppTaskApplicationService appTaskApplicationService;

    /**
     * 查询APP任务申请列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppTaskApplication appTaskApplication)
    {
        startPage();
        List<AppTaskApplication> list = appTaskApplicationService.selectAppTaskApplicationList(appTaskApplication);
        return getDataTable(list);
    }

    /**
     * 导出APP任务申请列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:export')")
    @Log(title = "APP任务申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppTaskApplication appTaskApplication)
    {
        List<AppTaskApplication> list = appTaskApplicationService.selectAppTaskApplicationList(appTaskApplication);
        ExcelUtil<AppTaskApplication> util = new ExcelUtil<AppTaskApplication>(AppTaskApplication.class);
        util.exportExcel(response, list, "APP任务申请数据");
    }

    /**
     * 获取APP任务申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:query')")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId)
    {
        return success(appTaskApplicationService.selectAppTaskApplicationByApplicationId(applicationId));
    }

    /**
     * 新增APP任务申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:add')")
    @Log(title = "APP任务申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppTaskApplication appTaskApplication)
    {
        appTaskApplication.setCreateBy(getUsername());
        return toAjax(appTaskApplicationService.insertAppTaskApplication(appTaskApplication));
    }

    /**
     * 修改APP任务申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:edit')")
    @Log(title = "APP任务申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppTaskApplication appTaskApplication)
    {
        return toAjax(appTaskApplicationService.updateAppTaskApplication(appTaskApplication));
    }

    /**
     * 删除APP任务申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:remove')")
    @Log(title = "APP任务申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds)
    {
        return toAjax(appTaskApplicationService.deleteAppTaskApplicationByApplicationIds(applicationIds));
    }

    /**
     * 根据任务ID查询申请列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:list')")
    @GetMapping("/task/{taskId}")
    public TableDataInfo getApplicationsByTaskId(@PathVariable Long taskId)
    {
        startPage();
        List<AppTaskApplication> list = appTaskApplicationService.selectAppTaskApplicationListByTaskId(taskId);
        return getDataTable(list);
    }

    /**
     * 管理员确认申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:confirm')")
    @Log(title = "确认申请", businessType = BusinessType.UPDATE)
    @PutMapping("/confirm/{applicationId}")
    public AjaxResult adminConfirmApplication(@PathVariable Long applicationId)
    {
        try {
            // 管理员操作，传入0作为发布者ID，在服务层需要特殊处理
            int result = appTaskApplicationService.confirmApplication(applicationId, 0L);
            return toAjax(result);
        } catch (Exception e) {
            return error("确认失败：" + e.getMessage());
        }
    }

    /**
     * 管理员拒绝申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:taskApplication:reject')")
    @Log(title = "拒绝申请", businessType = BusinessType.UPDATE)
    @PutMapping("/reject/{applicationId}")
    public AjaxResult adminRejectApplication(@PathVariable Long applicationId)
    {
        try {
            // 管理员操作，传入0作为发布者ID，在服务层需要特殊处理
            int result = appTaskApplicationService.rejectApplication(applicationId, 0L);
            return toAjax(result);
        } catch (Exception e) {
            return error("拒绝失败：" + e.getMessage());
        }
    }
}
