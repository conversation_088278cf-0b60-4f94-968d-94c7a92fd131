package com.ruoyi.web.controller.payment;

import java.util.List;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.service.IPaymentStatisticsService;

/**
 * 支付统计分析Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/payment/statistics")
public class PaymentStatisticsController extends BaseController
{
    @Autowired
    private IPaymentStatisticsService paymentStatisticsService;

    /**
     * 获取支付统计概览
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:query')")
    @GetMapping("/overview")
    public AjaxResult getPaymentOverview()
    {
        Map<String, Object> overview = paymentStatisticsService.getPaymentOverview();
        return success(overview);
    }

    /**
     * 获取支付趋势数据
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:trend')")
    @GetMapping("/trend")
    public AjaxResult getPaymentTrend(@RequestParam(defaultValue = "7d") String period)
    {
        List<Map<String, Object>> trendData = paymentStatisticsService.getPaymentTrend(period);
        return success(trendData);
    }

    /**
     * 获取支付渠道分布
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:channel')")
    @GetMapping("/channel")
    public AjaxResult getPaymentChannelDistribution()
    {
        List<Map<String, Object>> channelData = paymentStatisticsService.getPaymentChannelDistribution();
        return success(channelData);
    }

    /**
     * 获取各业务类型支付统计
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:query')")
    @GetMapping("/businessType")
    public AjaxResult getBusinessTypeStatistics()
    {
        List<Map<String, Object>> businessData = paymentStatisticsService.getBusinessTypeStatistics();
        return success(businessData);
    }

    /**
     * 获取支付异常统计
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:exception')")
    @GetMapping("/exception")
    public AjaxResult getPaymentExceptionStatistics()
    {
        Map<String, Object> exceptionStats = paymentStatisticsService.getPaymentExceptionStatistics();
        return success(exceptionStats);
    }

    /**
     * 获取今日支付统计
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:query')")
    @GetMapping("/today")
    public AjaxResult getTodayPaymentStatistics()
    {
        Map<String, Object> todayStats = paymentStatisticsService.getTodayPaymentStatistics();
        return success(todayStats);
    }

    /**
     * 获取本月支付统计
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:query')")
    @GetMapping("/month")
    public AjaxResult getMonthPaymentStatistics()
    {
        Map<String, Object> monthStats = paymentStatisticsService.getMonthPaymentStatistics();
        return success(monthStats);
    }

    /**
     * 获取支付成功率统计
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:trend')")
    @GetMapping("/successRate")
    public AjaxResult getPaymentSuccessRate(@RequestParam(defaultValue = "7d") String period)
    {
        List<Map<String, Object>> successRateData = paymentStatisticsService.getPaymentSuccessRate(period);
        return success(successRateData);
    }

    /**
     * 获取支付金额分布统计
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:query')")
    @GetMapping("/amountDistribution")
    public AjaxResult getPaymentAmountDistribution()
    {
        List<Map<String, Object>> amountDistribution = paymentStatisticsService.getPaymentAmountDistribution();
        return success(amountDistribution);
    }

    /**
     * 获取用户支付行为分析
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:query')")
    @GetMapping("/userBehavior")
    public AjaxResult getUserPaymentBehaviorAnalysis()
    {
        Map<String, Object> behaviorAnalysis = paymentStatisticsService.getUserPaymentBehaviorAnalysis();
        return success(behaviorAnalysis);
    }

    /**
     * 获取支付时段分析
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:query')")
    @GetMapping("/timeAnalysis")
    public AjaxResult getPaymentTimeAnalysis()
    {
        List<Map<String, Object>> timeAnalysis = paymentStatisticsService.getPaymentTimeAnalysis();
        return success(timeAnalysis);
    }

    /**
     * 获取退款统计
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:query')")
    @GetMapping("/refund")
    public AjaxResult getRefundStatistics()
    {
        Map<String, Object> refundStats = paymentStatisticsService.getRefundStatistics();
        return success(refundStats);
    }

    /**
     * 获取支付平台对比统计
     */
    @PreAuthorize("@ss.hasPermi('payment:statistics:channel')")
    @GetMapping("/platformComparison")
    public AjaxResult getPaymentPlatformComparison()
    {
        List<Map<String, Object>> platformComparison = paymentStatisticsService.getPaymentPlatformComparison();
        return success(platformComparison);
    }
}
