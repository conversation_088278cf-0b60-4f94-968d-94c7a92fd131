package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppActivity;
import com.ruoyi.fuguang.service.IAppActivityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP活动管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController("appActivityManageController")
@RequestMapping("/fuguang/activity")
public class AppActivityManageController extends BaseController
{
    @Autowired
    private IAppActivityService appActivityService;

    /**
     * 查询APP活动管理列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:activity:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppActivity appActivity)
    {
        startPage();
        List<AppActivity> list = appActivityService.selectAppActivityList(appActivity);
        return getDataTable(list);
    }

    /**
     * 导出APP活动管理列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:activity:export')")
    @Log(title = "APP活动管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppActivity appActivity)
    {
        List<AppActivity> list = appActivityService.selectAppActivityList(appActivity);
        ExcelUtil<AppActivity> util = new ExcelUtil<AppActivity>(AppActivity.class);
        util.exportExcel(response, list, "APP活动管理数据");
    }

    /**
     * 获取APP活动管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:activity:query')")
    @GetMapping(value = "/{activityId}")
    public AjaxResult getInfo(@PathVariable("activityId") Long activityId)
    {
        return success(appActivityService.selectAppActivityByActivityId(activityId));
    }

    /**
     * 新增APP活动管理
     */
    @PreAuthorize("@ss.hasPermi('fuguang:activity:add')")
    @Log(title = "APP活动管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppActivity appActivity)
    {
        appActivity.setCreateBy(getUsername());
        return toAjax(appActivityService.insertAppActivity(appActivity));
    }

    /**
     * 修改APP活动管理
     */
    @PreAuthorize("@ss.hasPermi('fuguang:activity:edit')")
    @Log(title = "APP活动管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppActivity appActivity)
    {
        appActivity.setUpdateBy(getUsername());
        return toAjax(appActivityService.updateAppActivity(appActivity));
    }

    /**
     * 删除APP活动管理
     */
    @PreAuthorize("@ss.hasPermi('fuguang:activity:remove')")
    @Log(title = "APP活动管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{activityIds}")
    public AjaxResult remove(@PathVariable Long[] activityIds)
    {
        return toAjax(appActivityService.deleteAppActivityByActivityIds(activityIds));
    }



    /**
     * 修改活动状态
     */
    @PreAuthorize("@ss.hasPermi('fuguang:activity:edit')")
    @Log(title = "修改活动状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody AppActivity appActivity)
    {
        appActivity.setUpdateBy(getUsername());
        return toAjax(appActivityService.updateAppActivity(appActivity));
    }
}
