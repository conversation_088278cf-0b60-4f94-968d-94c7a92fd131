package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.mapper.PaymentCallbackLogMapper;
import com.ruoyi.fuguang.domain.PaymentCallbackLog;
import com.ruoyi.fuguang.service.IPaymentCallbackLogService;
import com.ruoyi.fuguang.service.ITaskPaymentService;
import com.ruoyi.fuguang.service.IMallPaymentService;
import com.ruoyi.fuguang.service.IOfflinePaymentService;

/**
 * 支付回调日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class PaymentCallbackLogServiceImpl implements IPaymentCallbackLogService 
{
    private static final Logger log = LoggerFactory.getLogger(PaymentCallbackLogServiceImpl.class);

    @Autowired
    private PaymentCallbackLogMapper paymentCallbackLogMapper;

    @Autowired
    private ITaskPaymentService taskPaymentService;

    @Autowired
    private IMallPaymentService mallPaymentService;

    @Autowired
    private IOfflinePaymentService offlinePaymentService;

    /**
     * 查询支付回调日志
     * 
     * @param callbackId 支付回调日志主键
     * @return 支付回调日志
     */
    @Override
    public PaymentCallbackLog selectPaymentCallbackLogByCallbackId(Long callbackId)
    {
        return paymentCallbackLogMapper.selectPaymentCallbackLogByCallbackId(callbackId);
    }

    /**
     * 查询支付回调日志列表
     * 
     * @param paymentCallbackLog 支付回调日志
     * @return 支付回调日志
     */
    @Override
    public List<PaymentCallbackLog> selectPaymentCallbackLogList(PaymentCallbackLog paymentCallbackLog)
    {
        return paymentCallbackLogMapper.selectPaymentCallbackLogList(paymentCallbackLog);
    }

    /**
     * 新增支付回调日志
     * 
     * @param paymentCallbackLog 支付回调日志
     * @return 结果
     */
    @Override
    public int insertPaymentCallbackLog(PaymentCallbackLog paymentCallbackLog)
    {
        paymentCallbackLog.setCreateTime(new Date());
        paymentCallbackLog.setUpdateTime(new Date());
        return paymentCallbackLogMapper.insertPaymentCallbackLog(paymentCallbackLog);
    }

    /**
     * 修改支付回调日志
     * 
     * @param paymentCallbackLog 支付回调日志
     * @return 结果
     */
    @Override
    public int updatePaymentCallbackLog(PaymentCallbackLog paymentCallbackLog)
    {
        paymentCallbackLog.setUpdateTime(new Date());
        return paymentCallbackLogMapper.updatePaymentCallbackLog(paymentCallbackLog);
    }

    /**
     * 批量删除支付回调日志
     * 
     * @param callbackIds 需要删除的支付回调日志主键
     * @return 结果
     */
    @Override
    public int deletePaymentCallbackLogByCallbackIds(Long[] callbackIds)
    {
        return paymentCallbackLogMapper.deletePaymentCallbackLogByCallbackIds(callbackIds);
    }

    /**
     * 删除支付回调日志信息
     * 
     * @param callbackId 支付回调日志主键
     * @return 结果
     */
    @Override
    public int deletePaymentCallbackLogByCallbackId(Long callbackId)
    {
        return paymentCallbackLogMapper.deletePaymentCallbackLogByCallbackId(callbackId);
    }

    /**
     * 根据订单号查询回调日志
     * 
     * @param orderNo 订单号
     * @return 回调日志列表
     */
    @Override
    public List<PaymentCallbackLog> selectPaymentCallbackLogByOrderNo(String orderNo)
    {
        return paymentCallbackLogMapper.selectPaymentCallbackLogByOrderNo(orderNo);
    }

    /**
     * 记录支付回调日志
     * 
     * @param orderNo 订单号
     * @param businessType 业务类型
     * @param payType 支付方式
     * @param payAmount 支付金额
     * @param callbackParams 回调参数
     * @param success 是否成功
     * @param errorMsg 错误信息
     * @return 结果
     */
    @Override
    public int logPaymentCallback(String orderNo, String businessType, String payType, 
                                 BigDecimal payAmount, String callbackParams, 
                                 boolean success, String errorMsg)
    {
        PaymentCallbackLog callbackLog = new PaymentCallbackLog();
        callbackLog.setOrderNo(orderNo);
        callbackLog.setBusinessType(businessType);
        callbackLog.setPayType(payType);
        callbackLog.setPayAmount(payAmount);
        callbackLog.setCallbackParams(callbackParams);
        callbackLog.setCallbackStatus(success ? "1" : "0");
        callbackLog.setCallbackTime(new Date());
        callbackLog.setRetryCount(0);
        callbackLog.setErrorMsg(errorMsg);
        
        return insertPaymentCallbackLog(callbackLog);
    }

    /**
     * 获取回调统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getCallbackStatistics()
    {
        return paymentCallbackLogMapper.getCallbackStatistics();
    }

    /**
     * 获取今日回调统计
     * 
     * @return 今日统计信息
     */
    @Override
    public Map<String, Object> getTodayCallbackStatistics()
    {
        return paymentCallbackLogMapper.getTodayCallbackStatistics();
    }

    /**
     * 获取回调成功率统计（按业务类型）
     * 
     * @return 成功率统计
     */
    @Override
    public List<Map<String, Object>> getCallbackSuccessRateByBusinessType()
    {
        return paymentCallbackLogMapper.getCallbackSuccessRateByBusinessType();
    }

    /**
     * 获取回调趋势统计（最近7天）
     * 
     * @return 趋势统计
     */
    @Override
    public List<Map<String, Object>> getCallbackTrendStatistics()
    {
        return paymentCallbackLogMapper.getCallbackTrendStatistics();
    }

    /**
     * 重试回调
     * 
     * @param callbackId 回调日志ID
     * @return 结果
     */
    @Override
    public boolean retryCallback(Long callbackId)
    {
        try {
            PaymentCallbackLog callbackLog = selectPaymentCallbackLogByCallbackId(callbackId);
            if (callbackLog == null) {
                log.error("回调日志不存在，ID：{}", callbackId);
                return false;
            }

            // 更新重试次数
            paymentCallbackLogMapper.updateRetryCount(callbackId);

            // 根据业务类型重新处理回调
            String orderNo = callbackLog.getOrderNo();
            boolean result = false;

            if ("task".equals(callbackLog.getBusinessType())) {
                // 重新处理任务支付回调
                result = retryTaskPaymentCallback(orderNo);
            } else if ("mall".equals(callbackLog.getBusinessType())) {
                // 重新处理商城支付回调
                result = retryMallPaymentCallback(orderNo);
            } else if ("offline".equals(callbackLog.getBusinessType())) {
                // 重新处理线下支付回调
                result = retryOfflinePaymentCallback(orderNo);
            }

            // 更新回调状态
            if (result) {
                callbackLog.setCallbackStatus("1");
                callbackLog.setErrorMsg(null);
                updatePaymentCallbackLog(callbackLog);
            }

            return result;
        } catch (Exception e) {
            log.error("重试回调异常，ID：{}", callbackId, e);
            return false;
        }
    }

    /**
     * 批量重试回调
     * 
     * @param callbackIds 回调日志ID数组
     * @return 结果
     */
    @Override
    public boolean batchRetryCallback(Long[] callbackIds)
    {
        boolean allSuccess = true;
        for (Long callbackId : callbackIds) {
            if (!retryCallback(callbackId)) {
                allSuccess = false;
            }
        }
        return allSuccess;
    }

    /**
     * 重新处理任务支付回调
     */
    private boolean retryTaskPaymentCallback(String orderNo) {
        try {
            // 这里可以重新查询支付状态或重新处理支付逻辑
            // 暂时返回true，实际应该调用具体的重试逻辑
            log.info("重试任务支付回调，订单号：{}", orderNo);
            return true;
        } catch (Exception e) {
            log.error("重试任务支付回调失败，订单号：{}", orderNo, e);
            return false;
        }
    }

    /**
     * 重新处理商城支付回调
     */
    private boolean retryMallPaymentCallback(String orderNo) {
        try {
            log.info("重试商城支付回调，订单号：{}", orderNo);
            return true;
        } catch (Exception e) {
            log.error("重试商城支付回调失败，订单号：{}", orderNo, e);
            return false;
        }
    }

    /**
     * 重新处理线下支付回调
     */
    private boolean retryOfflinePaymentCallback(String orderNo) {
        try {
            log.info("重试线下支付回调，订单号：{}", orderNo);
            return true;
        } catch (Exception e) {
            log.error("重试线下支付回调失败，订单号：{}", orderNo, e);
            return false;
        }
    }
}
