package com.ruoyi.fuguang.service;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.fuguang.domain.UserBalance;

/**
 * 用户余额Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IUserBalanceService 
{
    /**
     * 查询用户余额
     * 
     * @param balanceId 用户余额主键
     * @return 用户余额
     */
    public UserBalance selectUserBalanceByBalanceId(Long balanceId);

    /**
     * 根据用户ID查询用户余额
     * 
     * @param userId 用户ID
     * @return 用户余额
     */
    public UserBalance selectUserBalanceByUserId(Long userId);

    /**
     * 查询用户余额列表
     * 
     * @param userBalance 用户余额
     * @return 用户余额集合
     */
    public List<UserBalance> selectUserBalanceList(UserBalance userBalance);

    /**
     * 新增用户余额
     * 
     * @param userBalance 用户余额
     * @return 结果
     */
    public int insertUserBalance(UserBalance userBalance);

    /**
     * 修改用户余额
     * 
     * @param userBalance 用户余额
     * @return 结果
     */
    public int updateUserBalance(UserBalance userBalance);

    /**
     * 批量删除用户余额
     * 
     * @param balanceIds 需要删除的用户余额主键集合
     * @return 结果
     */
    public int deleteUserBalanceByBalanceIds(Long[] balanceIds);

    /**
     * 删除用户余额信息
     * 
     * @param balanceId 用户余额主键
     * @return 结果
     */
    public int deleteUserBalanceByBalanceId(Long balanceId);

    /**
     * 初始化用户余额
     * 
     * @param userId 用户ID
     * @return 用户余额
     */
    public UserBalance initUserBalance(Long userId);



    /**
     * 冻结用户余额
     * 
     * @param userId 用户ID
     * @param amount 冻结金额
     * @return 结果
     */
    public boolean freezeBalance(Long userId, BigDecimal amount);

    /**
     * 解冻用户余额
     * 
     * @param userId 用户ID
     * @param amount 解冻金额
     * @return 结果
     */
    public boolean unfreezeBalance(Long userId, BigDecimal amount);
}
