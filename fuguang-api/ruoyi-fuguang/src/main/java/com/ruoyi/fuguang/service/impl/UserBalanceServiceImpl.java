package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.service.IAppUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.UserBalanceMapper;
import com.ruoyi.fuguang.domain.UserBalance;
import com.ruoyi.fuguang.domain.BalanceRecord;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.fuguang.service.IBalanceRecordService;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 用户余额Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class UserBalanceServiceImpl implements IUserBalanceService
{
    @Autowired
    private UserBalanceMapper userBalanceMapper;

    @Autowired
    private IBalanceRecordService balanceRecordService;

    @Autowired
    private ICommissionBillService commissionBillService;

    @Autowired
    private IAppUserService appUserService;

    /**
     * 查询用户余额
     * 
     * @param balanceId 用户余额主键
     * @return 用户余额
     */
    @Override
    public UserBalance selectUserBalanceByBalanceId(Long balanceId)
    {
        return userBalanceMapper.selectUserBalanceByBalanceId(balanceId);
    }

    /**
     * 根据用户ID查询用户余额
     * 
     * @param userId 用户ID
     * @return 用户余额
     */
    @Override
    public UserBalance selectUserBalanceByUserId(Long userId)
    {
        return userBalanceMapper.selectUserBalanceByUserId(userId);
    }

    /**
     * 查询用户余额列表
     * 
     * @param userBalance 用户余额
     * @return 用户余额
     */
    @Override
    public List<UserBalance> selectUserBalanceList(UserBalance userBalance)
    {
        return userBalanceMapper.selectUserBalanceList(userBalance);
    }

    /**
     * 新增用户余额
     * 
     * @param userBalance 用户余额
     * @return 结果
     */
    @Override
    public int insertUserBalance(UserBalance userBalance)
    {
        userBalance.setCreateTime(DateUtils.getNowDate());
        return userBalanceMapper.insertUserBalance(userBalance);
    }

    /**
     * 修改用户余额
     * 
     * @param userBalance 用户余额
     * @return 结果
     */
    @Override
    public int updateUserBalance(UserBalance userBalance)
    {
        userBalance.setUpdateTime(DateUtils.getNowDate());
        return userBalanceMapper.updateUserBalance(userBalance);
    }

    /**
     * 批量删除用户余额
     * 
     * @param balanceIds 需要删除的用户余额主键
     * @return 结果
     */
    @Override
    public int deleteUserBalanceByBalanceIds(Long[] balanceIds)
    {
        return userBalanceMapper.deleteUserBalanceByBalanceIds(balanceIds);
    }

    /**
     * 删除用户余额信息
     * 
     * @param balanceId 用户余额主键
     * @return 结果
     */
    @Override
    public int deleteUserBalanceByBalanceId(Long balanceId)
    {
        return userBalanceMapper.deleteUserBalanceByBalanceId(balanceId);
    }

    /**
     * 初始化用户余额
     * 
     * @param userId 用户ID
     * @return 用户余额
     */
    @Override
    public UserBalance initUserBalance(Long userId)
    {
        UserBalance userBalance = selectUserBalanceByUserId(userId);
        if (userBalance == null) {
            AppUser appUser=appUserService.selectAppUserByUserId(userId);
            userBalance = new UserBalance();
            userBalance.setUserId(userId);
            userBalance.setUserName(appUser.getNickName());
            userBalance.setTotalBalance(BigDecimal.ZERO);
            userBalance.setAvailableBalance(BigDecimal.ZERO);
            userBalance.setFrozenBalance(BigDecimal.ZERO);
            userBalance.setTotalIncome(BigDecimal.ZERO);
            userBalance.setTotalExpense(BigDecimal.ZERO);
            userBalance.setTotalWithdraw(BigDecimal.ZERO);
            insertUserBalance(userBalance);
        }
        return userBalance;
    }



    /**
     * 冻结用户余额
     *
     * @param userId 用户ID
     * @param amount 冻结金额
     * @return 结果
     */
    @Override
    public boolean freezeBalance(Long userId, BigDecimal amount)
    {
        // 调用余额记录服务统一处理冻结逻辑
        return balanceRecordService.freezeBalance(userId, amount, "admin_freeze", null, null, "管理员冻结余额");
    }

    /**
     * 解冻用户余额
     *
     * @param userId 用户ID
     * @param amount 解冻金额
     * @return 结果
     */
    @Override
    public boolean unfreezeBalance(Long userId, BigDecimal amount)
    {
        // 调用余额记录服务统一处理解冻逻辑
        return balanceRecordService.unfreezeBalance(userId, amount, "admin_unfreeze", null, null, "管理员解冻余额");
    }

}
