package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 线下支付订单对象 offline_payment
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class OfflinePayment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 支付记录ID */
    private Long paymentId;

    /** 商家ID（商家申请ID） */
    @Excel(name = "商家ID")
    private Long merchantId;

    /** 商家名称 */
    @Excel(name = "商家名称")
    private String merchantName;

    /** 支付订单号 */
    @Excel(name = "支付订单号")
    private String orderNo;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmount;

    /** 让利金额 */
    @Excel(name = "让利金额")
    private BigDecimal amountDiscounts;

    /** 支付方式（1支付宝 2微信 3余额） */
    @Excel(name = "支付方式", readConverterExp = "1=支付宝,2=微信,3=余额")
    private String payType;

    /** 支付状态（0待支付 1支付成功 2支付失败 3已退款） */
    @Excel(name = "支付状态", readConverterExp = "0=待支付,1=支付成功,2=支付失败,3=已退款")
    private String payStatus;

    /** 第三方交易号 */
    @Excel(name = "第三方交易号")
    private String tradeNo;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 通知时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "通知时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date notifyTime;

    /** 转账状态（0未转账 1转账中 2转账成功 3转账失败） */
    @Excel(name = "转账状态", readConverterExp = "0=未转账,1=转账中,2=转账成功,3=转账失败")
    private String transferStatus;

    /** 转账单号 */
    @Excel(name = "转账单号")
    private String transferNo;

    /** 转账时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "转账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date transferTime;

    /** 转账金额 */
    @Excel(name = "转账金额")
    private BigDecimal transferAmount;

    /** 平台手续费 */
    @Excel(name = "平台手续费")
    private BigDecimal platformFee;

    /** 支付人员 */
    @Excel(name = "支付人员")
    private Long payUserId;

    /** 支付字符串 */
    @Excel(name = "支付字符串")
    private String payOrderString;

}
