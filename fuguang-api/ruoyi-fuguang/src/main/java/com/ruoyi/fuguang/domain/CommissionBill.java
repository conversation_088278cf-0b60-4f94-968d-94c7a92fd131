package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 佣金账单对象 commission_bill
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public class CommissionBill extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账单ID */
    private Long billId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String userName;

    /** 账单年份 */
    @Excel(name = "账单年份")
    private Integer billYear;

    /** 账单月份 */
    @Excel(name = "账单月份")
    private Integer billMonth;

    /** 总收入 */
    @Excel(name = "总收入")
    private BigDecimal totalIncome;

    /** 任务佣金收入 */
    @Excel(name = "任务佣金收入")
    private BigDecimal taskCommission;

    /** 推荐奖励收入 */
    @Excel(name = "推荐奖励收入")
    private BigDecimal recommendReward;

    /** 其他收入 */
    @Excel(name = "其他收入")
    private BigDecimal otherIncome;

    /** 总支出 */
    @Excel(name = "总支出")
    private BigDecimal totalExpense;

    /** 总提现 */
    @Excel(name = "总提现")
    private BigDecimal totalWithdraw;

    /** 提现次数 */
    @Excel(name = "提现次数")
    private Integer withdrawCount;

    public void setBillId(Long billId) 
    {
        this.billId = billId;
    }

    public Long getBillId() 
    {
        return billId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setBillYear(Integer billYear) 
    {
        this.billYear = billYear;
    }

    public Integer getBillYear() 
    {
        return billYear;
    }

    public void setBillMonth(Integer billMonth) 
    {
        this.billMonth = billMonth;
    }

    public Integer getBillMonth() 
    {
        return billMonth;
    }

    public void setTotalIncome(BigDecimal totalIncome) 
    {
        this.totalIncome = totalIncome;
    }

    public BigDecimal getTotalIncome() 
    {
        return totalIncome;
    }

    public void setTaskCommission(BigDecimal taskCommission) 
    {
        this.taskCommission = taskCommission;
    }

    public BigDecimal getTaskCommission() 
    {
        return taskCommission;
    }

    public void setRecommendReward(BigDecimal recommendReward) 
    {
        this.recommendReward = recommendReward;
    }

    public BigDecimal getRecommendReward() 
    {
        return recommendReward;
    }

    public void setOtherIncome(BigDecimal otherIncome) 
    {
        this.otherIncome = otherIncome;
    }

    public BigDecimal getOtherIncome()
    {
        return otherIncome;
    }

    public void setTotalExpense(BigDecimal totalExpense)
    {
        this.totalExpense = totalExpense;
    }

    public BigDecimal getTotalExpense()
    {
        return totalExpense;
    }

    public void setTotalWithdraw(BigDecimal totalWithdraw)
    {
        this.totalWithdraw = totalWithdraw;
    }

    public BigDecimal getTotalWithdraw() 
    {
        return totalWithdraw;
    }

    public void setWithdrawCount(Integer withdrawCount) 
    {
        this.withdrawCount = withdrawCount;
    }

    public Integer getWithdrawCount() 
    {
        return withdrawCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("billId", getBillId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("billYear", getBillYear())
            .append("billMonth", getBillMonth())
            .append("totalIncome", getTotalIncome())
            .append("taskCommission", getTaskCommission())
            .append("recommendReward", getRecommendReward())
            .append("otherIncome", getOtherIncome())
            .append("totalExpense", getTotalExpense())
            .append("totalWithdraw", getTotalWithdraw())
            .append("withdrawCount", getWithdrawCount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
