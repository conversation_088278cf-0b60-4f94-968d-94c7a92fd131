package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.service.IPaymentStatisticsService;
import com.ruoyi.fuguang.mapper.TaskPaymentMapper;
import com.ruoyi.fuguang.mapper.MallPaymentMapper;
import com.ruoyi.fuguang.mapper.OfflinePaymentMapper;

/**
 * 支付统计分析Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class PaymentStatisticsServiceImpl implements IPaymentStatisticsService 
{
    private static final Logger log = LoggerFactory.getLogger(PaymentStatisticsServiceImpl.class);

    @Autowired
    private TaskPaymentMapper taskPaymentMapper;

    @Autowired
    private MallPaymentMapper mallPaymentMapper;

    @Autowired
    private OfflinePaymentMapper offlinePaymentMapper;

    /**
     * 获取支付统计概览
     *
     * @return 统计概览数据
     */
    @Override
    public Map<String, Object> getPaymentOverview()
    {
        Map<String, Object> overview = new HashMap<>();

        try {
            log.info("开始获取支付统计概览数据");

            // 获取今日统计
            Map<String, Object> todayStats = getTodayPaymentStatistics();
            log.info("今日统计数据: {}", todayStats);

            overview.put("todayAmount", todayStats.get("todayAmount"));
            overview.put("todayCount", todayStats.get("todayCount"));
            // 修复字段映射：前端期望 successRate，不是 todaySuccessRate
            overview.put("successRate", todayStats.get("todaySuccessRate"));

            // 暂时设置默认增长率，后续可以通过数据库查询昨日数据来计算
            overview.put("todayGrowth", BigDecimal.ZERO);
            overview.put("todayCountGrowth", BigDecimal.ZERO);
            overview.put("successRateGrowth", BigDecimal.ZERO);

            // 获取本月统计
            Map<String, Object> monthStats = getMonthPaymentStatistics();
            log.info("本月统计数据: {}", monthStats);

            overview.put("monthAmount", monthStats.get("monthAmount"));
            overview.put("monthCount", monthStats.get("monthCount"));
            overview.put("monthSuccessRate", monthStats.get("monthSuccessRate"));

            // 汇总所有支付类型的总体统计
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalSuccessAmount = BigDecimal.ZERO;
            int totalCount = 0;
            int totalSuccessCount = 0;
            int totalFailCount = 0;
            int totalRefundCount = 0;
            BigDecimal totalRefundAmount = BigDecimal.ZERO;

            // 获取任务支付统计
            Map<String, Object> taskStats = taskPaymentMapper.getPaymentOverviewStatistics();
            if (taskStats != null) {
                totalAmount = totalAmount.add(getBigDecimalValue(taskStats.get("totalAmount")));
                totalSuccessAmount = totalSuccessAmount.add(getBigDecimalValue(taskStats.get("successAmount")));
                totalCount += getIntValue(taskStats.get("totalCount"));
                totalSuccessCount += getIntValue(taskStats.get("successCount"));
                totalFailCount += getIntValue(taskStats.get("failCount"));
                totalRefundCount += getIntValue(taskStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(taskStats.get("refundAmount")));
            }

            // 获取商城支付统计
            Map<String, Object> mallStats = mallPaymentMapper.getPaymentOverviewStatistics();
            if (mallStats != null) {
                totalAmount = totalAmount.add(getBigDecimalValue(mallStats.get("totalAmount")));
                totalSuccessAmount = totalSuccessAmount.add(getBigDecimalValue(mallStats.get("successAmount")));
                totalCount += getIntValue(mallStats.get("totalCount"));
                totalSuccessCount += getIntValue(mallStats.get("successCount"));
                totalFailCount += getIntValue(mallStats.get("failCount"));
                totalRefundCount += getIntValue(mallStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(mallStats.get("refundAmount")));
            }

            // 获取线下支付统计
            Map<String, Object> offlineStats = offlinePaymentMapper.getPaymentOverviewStatistics();
            if (offlineStats != null) {
                totalAmount = totalAmount.add(getBigDecimalValue(offlineStats.get("totalAmount")));
                totalSuccessAmount = totalSuccessAmount.add(getBigDecimalValue(offlineStats.get("successAmount")));
                totalCount += getIntValue(offlineStats.get("totalCount"));
                totalSuccessCount += getIntValue(offlineStats.get("successCount"));
                totalFailCount += getIntValue(offlineStats.get("failCount"));
                totalRefundCount += getIntValue(offlineStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(offlineStats.get("refundAmount")));
            }

            // 计算总体成功率
            BigDecimal successRate = totalCount > 0 ?
                BigDecimal.valueOf(totalSuccessCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

            overview.put("totalAmount", totalAmount);
            overview.put("totalCount", totalCount);
            overview.put("successRate", successRate);
            overview.put("successCount", totalSuccessCount);
            overview.put("successAmount", totalSuccessAmount);
            overview.put("failedCount", totalFailCount);
            overview.put("refundCount", totalRefundCount);
            overview.put("refundAmount", totalRefundAmount);

        } catch (Exception e) {
            log.error("获取支付统计概览异常", e);
            // 返回默认值
            overview.put("todayAmount", BigDecimal.ZERO);
            overview.put("todayCount", 0);
            overview.put("todaySuccessRate", BigDecimal.ZERO);
            overview.put("monthAmount", BigDecimal.ZERO);
            overview.put("monthCount", 0);
            overview.put("monthSuccessRate", BigDecimal.ZERO);
            overview.put("totalAmount", BigDecimal.ZERO);
            overview.put("totalCount", 0);
            overview.put("successRate", BigDecimal.ZERO);
            overview.put("successCount", 0);
            overview.put("successAmount", BigDecimal.ZERO);
            overview.put("failedCount", 0);
            overview.put("refundCount", 0);
            overview.put("refundAmount", BigDecimal.ZERO);
        }

        return overview;
    }

    /**
     * 获取支付趋势数据
     *
     * @param period 时间周期（7d, 30d, 90d）
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getPaymentTrend(String period)
    {
        List<Map<String, Object>> trendData = new ArrayList<>();

        try {
            int days = 7; // 默认7天
            if ("30d".equals(period)) {
                days = 30;
            } else if ("90d".equals(period)) {
                days = 90;
            }

            // 使用Map来汇总各个支付类型的数据
            Map<String, Map<String, Object>> dateDataMap = new HashMap<>();

            // 获取任务支付趋势数据
            List<Map<String, Object>> taskTrendData = taskPaymentMapper.getPaymentTrendStatistics(days);
            if (taskTrendData != null) {
                for (Map<String, Object> data : taskTrendData) {
                    String date = data.get("date").toString();
                    Map<String, Object> dayData = dateDataMap.getOrDefault(date, new HashMap<>());
                    dayData.put("date", data.get("date"));
                    dayData.put("count", getIntValue(dayData.get("count")) + getIntValue(data.get("count")));
                    dayData.put("amount", getBigDecimalValue(dayData.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    dayData.put("successCount", getIntValue(dayData.get("successCount")) + getIntValue(data.get("successCount")));
                    dayData.put("successAmount", getBigDecimalValue(dayData.get("successAmount")).add(getBigDecimalValue(data.get("successAmount"))));
                    dateDataMap.put(date, dayData);
                }
            }

            // 获取商城支付趋势数据
            List<Map<String, Object>> mallTrendData = mallPaymentMapper.getPaymentTrendStatistics(days);
            if (mallTrendData != null) {
                for (Map<String, Object> data : mallTrendData) {
                    String date = data.get("date").toString();
                    Map<String, Object> dayData = dateDataMap.getOrDefault(date, new HashMap<>());
                    dayData.put("date", data.get("date"));
                    dayData.put("count", getIntValue(dayData.get("count")) + getIntValue(data.get("count")));
                    dayData.put("amount", getBigDecimalValue(dayData.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    dayData.put("successCount", getIntValue(dayData.get("successCount")) + getIntValue(data.get("successCount")));
                    dayData.put("successAmount", getBigDecimalValue(dayData.get("successAmount")).add(getBigDecimalValue(data.get("successAmount"))));
                    dateDataMap.put(date, dayData);
                }
            }

            // 获取线下支付趋势数据
            List<Map<String, Object>> offlineTrendData = offlinePaymentMapper.getPaymentTrendStatistics(days);
            if (offlineTrendData != null) {
                for (Map<String, Object> data : offlineTrendData) {
                    String date = data.get("date").toString();
                    Map<String, Object> dayData = dateDataMap.getOrDefault(date, new HashMap<>());
                    dayData.put("date", data.get("date"));
                    dayData.put("count", getIntValue(dayData.get("count")) + getIntValue(data.get("count")));
                    dayData.put("amount", getBigDecimalValue(dayData.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    dayData.put("successCount", getIntValue(dayData.get("successCount")) + getIntValue(data.get("successCount")));
                    dayData.put("successAmount", getBigDecimalValue(dayData.get("successAmount")).add(getBigDecimalValue(data.get("successAmount"))));
                    dateDataMap.put(date, dayData);
                }
            }

            // 计算成功率并转换为列表
            for (Map<String, Object> dayData : dateDataMap.values()) {
                int totalCount = getIntValue(dayData.get("count"));
                int successCount = getIntValue(dayData.get("successCount"));
                BigDecimal successRate = totalCount > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                dayData.put("successRate", successRate);
                trendData.add(dayData);
            }

            // 按日期排序
            trendData.sort((a, b) -> b.get("date").toString().compareTo(a.get("date").toString()));

        } catch (Exception e) {
            log.error("获取支付趋势数据异常", e);
        }

        return trendData;
    }

    /**
     * 获取支付渠道分布
     *
     * @return 渠道分布数据
     */
    @Override
    public List<Map<String, Object>> getPaymentChannelDistribution()
    {
        List<Map<String, Object>> channelData = new ArrayList<>();

        try {
            log.info("开始获取支付渠道分布数据");

            // 使用Map来汇总各个支付类型的渠道数据
            Map<String, Map<String, Object>> channelMap = new HashMap<>();

            // 获取任务支付渠道分布
            List<Map<String, Object>> taskChannelData = taskPaymentMapper.getPaymentChannelDistribution();
            log.info("任务支付渠道分布数据: {}", taskChannelData);
            if (taskChannelData != null) {
                for (Map<String, Object> data : taskChannelData) {
                    String payType = data.get("payType").toString();
                    Map<String, Object> channelInfo = channelMap.getOrDefault(payType, new HashMap<>());
                    channelInfo.put("payType", payType);
                    channelInfo.put("payTypeName", data.get("payTypeName"));
                    channelInfo.put("count", getIntValue(channelInfo.get("count")) + getIntValue(data.get("count")));
                    channelInfo.put("amount", getBigDecimalValue(channelInfo.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    channelInfo.put("successCount", getIntValue(channelInfo.get("successCount")) + getIntValue(data.get("successCount")));
                    channelMap.put(payType, channelInfo);
                }
            }

            // 获取商城支付渠道分布
            List<Map<String, Object>> mallChannelData = mallPaymentMapper.getPaymentChannelDistribution();
            if (mallChannelData != null) {
                for (Map<String, Object> data : mallChannelData) {
                    String payType = data.get("payType").toString();
                    Map<String, Object> channelInfo = channelMap.getOrDefault(payType, new HashMap<>());
                    channelInfo.put("payType", payType);
                    channelInfo.put("payTypeName", data.get("payTypeName"));
                    channelInfo.put("count", getIntValue(channelInfo.get("count")) + getIntValue(data.get("count")));
                    channelInfo.put("amount", getBigDecimalValue(channelInfo.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    channelInfo.put("successCount", getIntValue(channelInfo.get("successCount")) + getIntValue(data.get("successCount")));
                    channelMap.put(payType, channelInfo);
                }
            }

            // 获取线下支付渠道分布
            List<Map<String, Object>> offlineChannelData = offlinePaymentMapper.getPaymentChannelDistribution();
            if (offlineChannelData != null) {
                for (Map<String, Object> data : offlineChannelData) {
                    String payType = data.get("payType").toString();
                    Map<String, Object> channelInfo = channelMap.getOrDefault(payType, new HashMap<>());
                    channelInfo.put("payType", payType);
                    channelInfo.put("payTypeName", data.get("payTypeName"));
                    channelInfo.put("count", getIntValue(channelInfo.get("count")) + getIntValue(data.get("count")));
                    channelInfo.put("amount", getBigDecimalValue(channelInfo.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    channelInfo.put("successCount", getIntValue(channelInfo.get("successCount")) + getIntValue(data.get("successCount")));
                    channelMap.put(payType, channelInfo);
                }
            }

            // 计算总数用于百分比计算
            int totalCount = channelMap.values().stream().mapToInt(data -> getIntValue(data.get("count"))).sum();

            // 构建返回数据并计算成功率和占比
            for (Map<String, Object> channelInfo : channelMap.values()) {
                int count = getIntValue(channelInfo.get("count"));
                int successCount = getIntValue(channelInfo.get("successCount"));

                // 计算成功率
                BigDecimal successRate = count > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / count).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                channelInfo.put("successRate", successRate);

                // 计算占比
                BigDecimal percentage = totalCount > 0 ?
                    BigDecimal.valueOf(count * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                channelInfo.put("percentage", percentage);

                channelData.add(channelInfo);
            }

            // 按数量排序
            channelData.sort((a, b) -> Integer.compare(getIntValue(b.get("count")), getIntValue(a.get("count"))));

            log.info("最终渠道分布数据: {}", channelData);

        } catch (Exception e) {
            log.error("获取支付渠道分布异常", e);
        }

        return channelData;
    }

    /**
     * 获取各业务类型支付统计
     *
     * @return 业务类型统计数据
     */
    @Override
    public List<Map<String, Object>> getBusinessTypeStatistics()
    {
        List<Map<String, Object>> businessData = new ArrayList<>();

        try {
            log.info("开始获取各业务类型支付统计");

            // 任务支付统计
            Map<String, Object> taskStats = taskPaymentMapper.getPaymentOverviewStatistics();
            Map<String, Object> taskTodayStats = taskPaymentMapper.getTodayPaymentStatistics();
            Map<String, Object> taskMonthStats = taskPaymentMapper.getMonthPaymentStatistics();

            if (taskStats != null) {
                Map<String, Object> taskBusinessStats = new HashMap<>();
                taskBusinessStats.put("businessType", "task");
                taskBusinessStats.put("businessName", "任务支付");
                taskBusinessStats.put("count", getIntValue(taskStats.get("totalCount")));
                taskBusinessStats.put("amount", getBigDecimalValue(taskStats.get("totalAmount")));
                taskBusinessStats.put("successCount", getIntValue(taskStats.get("successCount")));
                taskBusinessStats.put("successAmount", getBigDecimalValue(taskStats.get("successAmount")));

                // 添加前端期望的字段
                taskBusinessStats.put("todayAmount", taskTodayStats != null ? getBigDecimalValue(taskTodayStats.get("todayAmount")) : BigDecimal.ZERO);
                taskBusinessStats.put("monthAmount", taskMonthStats != null ? getBigDecimalValue(taskMonthStats.get("monthAmount")) : BigDecimal.ZERO);
                taskBusinessStats.put("pendingCount", 0); // 待支付订单数，需要根据实际业务逻辑获取

                int totalCount = getIntValue(taskStats.get("totalCount"));
                int successCount = getIntValue(taskStats.get("successCount"));
                BigDecimal successRate = totalCount > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                taskBusinessStats.put("successRate", successRate);

                log.info("任务支付统计数据: {}", taskBusinessStats);
                businessData.add(taskBusinessStats);
            }

            // 商城支付统计
            Map<String, Object> mallStats = mallPaymentMapper.getPaymentOverviewStatistics();
            Map<String, Object> mallTodayStats = mallPaymentMapper.getTodayPaymentStatistics();
            Map<String, Object> mallMonthStats = mallPaymentMapper.getMonthPaymentStatistics();

            if (mallStats != null) {
                Map<String, Object> mallBusinessStats = new HashMap<>();
                mallBusinessStats.put("businessType", "mall");
                mallBusinessStats.put("businessName", "商城支付");
                mallBusinessStats.put("count", getIntValue(mallStats.get("totalCount")));
                mallBusinessStats.put("amount", getBigDecimalValue(mallStats.get("totalAmount")));
                mallBusinessStats.put("successCount", getIntValue(mallStats.get("successCount")));
                mallBusinessStats.put("successAmount", getBigDecimalValue(mallStats.get("successAmount")));

                // 添加前端期望的字段
                mallBusinessStats.put("todayAmount", mallTodayStats != null ? getBigDecimalValue(mallTodayStats.get("todayAmount")) : BigDecimal.ZERO);
                mallBusinessStats.put("monthAmount", mallMonthStats != null ? getBigDecimalValue(mallMonthStats.get("monthAmount")) : BigDecimal.ZERO);
                mallBusinessStats.put("pendingCount", 0); // 待支付订单数，需要根据实际业务逻辑获取

                int totalCount = getIntValue(mallStats.get("totalCount"));
                int successCount = getIntValue(mallStats.get("successCount"));
                BigDecimal successRate = totalCount > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                mallBusinessStats.put("successRate", successRate);

                log.info("商城支付统计数据: {}", mallBusinessStats);
                businessData.add(mallBusinessStats);
            }

            // 线下支付统计
            Map<String, Object> offlineStats = offlinePaymentMapper.getPaymentOverviewStatistics();
            Map<String, Object> offlineTodayStats = offlinePaymentMapper.getTodayPaymentStatistics();
            Map<String, Object> offlineMonthStats = offlinePaymentMapper.getMonthPaymentStatistics();

            if (offlineStats != null) {
                Map<String, Object> offlineBusinessStats = new HashMap<>();
                offlineBusinessStats.put("businessType", "offline");
                offlineBusinessStats.put("businessName", "线下支付");
                offlineBusinessStats.put("count", getIntValue(offlineStats.get("totalCount")));
                offlineBusinessStats.put("amount", getBigDecimalValue(offlineStats.get("totalAmount")));
                offlineBusinessStats.put("successCount", getIntValue(offlineStats.get("successCount")));
                offlineBusinessStats.put("successAmount", getBigDecimalValue(offlineStats.get("successAmount")));

                // 添加前端期望的字段 - 线下支付使用不同的字段名
                offlineBusinessStats.put("todayAmount", offlineTodayStats != null ? getBigDecimalValue(offlineTodayStats.get("todayAmount")) : BigDecimal.ZERO);
                offlineBusinessStats.put("monthAmount", offlineMonthStats != null ? getBigDecimalValue(offlineMonthStats.get("monthAmount")) : BigDecimal.ZERO);
                offlineBusinessStats.put("pendingTransfer", 0); // 待转账订单数，需要根据实际业务逻辑获取

                int totalCount = getIntValue(offlineStats.get("totalCount"));
                int successCount = getIntValue(offlineStats.get("successCount"));
                BigDecimal transferRate = totalCount > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                offlineBusinessStats.put("transferRate", transferRate); // 线下支付使用转账成功率

                log.info("线下支付统计数据: {}", offlineBusinessStats);
                businessData.add(offlineBusinessStats);
            }

        } catch (Exception e) {
            log.error("获取业务类型支付统计异常", e);
        }
        
        return businessData;
    }

    /**
     * 获取支付异常统计
     * 
     * @return 异常统计数据
     */
    @Override
    public Map<String, Object> getPaymentExceptionStatistics()
    {
        Map<String, Object> exceptionStats = new HashMap<>();
        
        try {
            int totalFailCount = 0;
            BigDecimal totalFailAmount = BigDecimal.ZERO;
            int totalTimeoutCount = 0;
            int totalRefundCount = 0;
            BigDecimal totalRefundAmount = BigDecimal.ZERO;

            // 汇总任务支付异常统计
            Map<String, Object> taskExceptionStats = taskPaymentMapper.getPaymentExceptionStatistics();
            if (taskExceptionStats != null) {
                totalFailCount += getIntValue(taskExceptionStats.get("failCount"));
                totalFailAmount = totalFailAmount.add(getBigDecimalValue(taskExceptionStats.get("failAmount")));
                totalTimeoutCount += getIntValue(taskExceptionStats.get("timeoutCount"));
                totalRefundCount += getIntValue(taskExceptionStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(taskExceptionStats.get("refundAmount")));
            }

            // 汇总商城支付异常统计
            Map<String, Object> mallExceptionStats = mallPaymentMapper.getPaymentExceptionStatistics();
            if (mallExceptionStats != null) {
                totalFailCount += getIntValue(mallExceptionStats.get("failCount"));
                totalFailAmount = totalFailAmount.add(getBigDecimalValue(mallExceptionStats.get("failAmount")));
                totalTimeoutCount += getIntValue(mallExceptionStats.get("timeoutCount"));
                totalRefundCount += getIntValue(mallExceptionStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(mallExceptionStats.get("refundAmount")));
            }

            // 汇总线下支付异常统计
            Map<String, Object> offlineExceptionStats = offlinePaymentMapper.getPaymentExceptionStatistics();
            if (offlineExceptionStats != null) {
                totalFailCount += getIntValue(offlineExceptionStats.get("failCount"));
                totalFailAmount = totalFailAmount.add(getBigDecimalValue(offlineExceptionStats.get("failAmount")));
                totalTimeoutCount += getIntValue(offlineExceptionStats.get("timeoutCount"));
                totalRefundCount += getIntValue(offlineExceptionStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(offlineExceptionStats.get("refundAmount")));
            }

            // 计算异常率（失败+超时）
            Map<String, Object> overviewStats = getPaymentOverview();
            int totalCount = getIntValue(overviewStats.get("totalCount"));
            BigDecimal exceptionRate = totalCount > 0 ?
                BigDecimal.valueOf((totalFailCount + totalTimeoutCount) * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

            exceptionStats.put("failedCount", totalFailCount);
            exceptionStats.put("failedAmount", totalFailAmount);
            exceptionStats.put("timeoutCount", totalTimeoutCount);
            exceptionStats.put("refundCount", totalRefundCount);
            exceptionStats.put("refundAmount", totalRefundAmount);
            exceptionStats.put("exceptionRate", exceptionRate);
            exceptionStats.put("totalExceptions", totalFailCount + totalTimeoutCount);

        } catch (Exception e) {
            log.error("获取支付异常统计异常", e);
            exceptionStats.put("failedCount", 0);
            exceptionStats.put("failedAmount", BigDecimal.ZERO);
            exceptionStats.put("timeoutCount", 0);
            exceptionStats.put("refundCount", 0);
            exceptionStats.put("refundAmount", BigDecimal.ZERO);
            exceptionStats.put("exceptionRate", BigDecimal.ZERO);
            exceptionStats.put("totalExceptions", 0);
        }
        
        return exceptionStats;
    }

    /**
     * 获取今日支付统计
     *
     * @return 今日统计数据
     */
    @Override
    public Map<String, Object> getTodayPaymentStatistics()
    {
        Map<String, Object> todayStats = new HashMap<>();

        try {
            BigDecimal todayAmount = BigDecimal.ZERO;
            int todayCount = 0;
            BigDecimal todaySuccessAmount = BigDecimal.ZERO;
            int todaySuccessCount = 0;

            // 汇总任务支付今日统计
            Map<String, Object> taskTodayStats = taskPaymentMapper.getTodayPaymentStatistics();
            if (taskTodayStats != null) {
                todayAmount = todayAmount.add(getBigDecimalValue(taskTodayStats.get("todayAmount")));
                todayCount += getIntValue(taskTodayStats.get("todayCount"));
                todaySuccessAmount = todaySuccessAmount.add(getBigDecimalValue(taskTodayStats.get("todaySuccessAmount")));
                todaySuccessCount += getIntValue(taskTodayStats.get("todaySuccessCount"));
            }

            // 汇总商城支付今日统计
            Map<String, Object> mallTodayStats = mallPaymentMapper.getTodayPaymentStatistics();
            if (mallTodayStats != null) {
                todayAmount = todayAmount.add(getBigDecimalValue(mallTodayStats.get("todayAmount")));
                todayCount += getIntValue(mallTodayStats.get("todayCount"));
                todaySuccessAmount = todaySuccessAmount.add(getBigDecimalValue(mallTodayStats.get("todaySuccessAmount")));
                todaySuccessCount += getIntValue(mallTodayStats.get("todaySuccessCount"));
            }

            // 汇总线下支付今日统计
            Map<String, Object> offlineTodayStats = offlinePaymentMapper.getTodayPaymentStatistics();
            if (offlineTodayStats != null) {
                todayAmount = todayAmount.add(getBigDecimalValue(offlineTodayStats.get("todayAmount")));
                todayCount += getIntValue(offlineTodayStats.get("todayCount"));
                todaySuccessAmount = todaySuccessAmount.add(getBigDecimalValue(offlineTodayStats.get("todaySuccessAmount")));
                todaySuccessCount += getIntValue(offlineTodayStats.get("todaySuccessCount"));
            }

            // 计算今日成功率
            BigDecimal todaySuccessRate = todayCount > 0 ?
                BigDecimal.valueOf(todaySuccessCount * 100.0 / todayCount).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

            todayStats.put("todayAmount", todayAmount);
            todayStats.put("todayCount", todayCount);
            todayStats.put("todaySuccessAmount", todaySuccessAmount);
            todayStats.put("todaySuccessCount", todaySuccessCount);
            todayStats.put("todaySuccessRate", todaySuccessRate);

        } catch (Exception e) {
            log.error("获取今日支付统计异常", e);
            todayStats.put("todayAmount", BigDecimal.ZERO);
            todayStats.put("todayCount", 0);
            todayStats.put("todaySuccessAmount", BigDecimal.ZERO);
            todayStats.put("todaySuccessCount", 0);
            todayStats.put("todaySuccessRate", BigDecimal.ZERO);
        }

        return todayStats;
    }

    /**
     * 获取本月支付统计
     *
     * @return 本月统计数据
     */
    @Override
    public Map<String, Object> getMonthPaymentStatistics()
    {
        Map<String, Object> monthStats = new HashMap<>();

        try {
            BigDecimal monthAmount = BigDecimal.ZERO;
            int monthCount = 0;
            BigDecimal monthSuccessAmount = BigDecimal.ZERO;
            int monthSuccessCount = 0;

            // 汇总任务支付本月统计
            Map<String, Object> taskMonthStats = taskPaymentMapper.getMonthPaymentStatistics();
            if (taskMonthStats != null) {
                monthAmount = monthAmount.add(getBigDecimalValue(taskMonthStats.get("monthAmount")));
                monthCount += getIntValue(taskMonthStats.get("monthCount"));
                monthSuccessAmount = monthSuccessAmount.add(getBigDecimalValue(taskMonthStats.get("monthSuccessAmount")));
                monthSuccessCount += getIntValue(taskMonthStats.get("monthSuccessCount"));
            }

            // 汇总商城支付本月统计
            Map<String, Object> mallMonthStats = mallPaymentMapper.getMonthPaymentStatistics();
            if (mallMonthStats != null) {
                monthAmount = monthAmount.add(getBigDecimalValue(mallMonthStats.get("monthAmount")));
                monthCount += getIntValue(mallMonthStats.get("monthCount"));
                monthSuccessAmount = monthSuccessAmount.add(getBigDecimalValue(mallMonthStats.get("monthSuccessAmount")));
                monthSuccessCount += getIntValue(mallMonthStats.get("monthSuccessCount"));
            }

            // 汇总线下支付本月统计
            Map<String, Object> offlineMonthStats = offlinePaymentMapper.getMonthPaymentStatistics();
            if (offlineMonthStats != null) {
                monthAmount = monthAmount.add(getBigDecimalValue(offlineMonthStats.get("monthAmount")));
                monthCount += getIntValue(offlineMonthStats.get("monthCount"));
                monthSuccessAmount = monthSuccessAmount.add(getBigDecimalValue(offlineMonthStats.get("monthSuccessAmount")));
                monthSuccessCount += getIntValue(offlineMonthStats.get("monthSuccessCount"));
            }

            // 计算本月成功率
            BigDecimal monthSuccessRate = monthCount > 0 ?
                BigDecimal.valueOf(monthSuccessCount * 100.0 / monthCount).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

            monthStats.put("monthAmount", monthAmount);
            monthStats.put("monthCount", monthCount);
            monthStats.put("monthSuccessAmount", monthSuccessAmount);
            monthStats.put("monthSuccessCount", monthSuccessCount);
            monthStats.put("monthSuccessRate", monthSuccessRate);

        } catch (Exception e) {
            log.error("获取本月支付统计异常", e);
            monthStats.put("monthAmount", BigDecimal.ZERO);
            monthStats.put("monthCount", 0);
            monthStats.put("monthSuccessAmount", BigDecimal.ZERO);
            monthStats.put("monthSuccessCount", 0);
            monthStats.put("monthSuccessRate", BigDecimal.ZERO);
        }

        return monthStats;
    }

    // 其他方法的实现...
    @Override
    public List<Map<String, Object>> getPaymentSuccessRate(String period) {
        List<Map<String, Object>> successRateData = new ArrayList<>();

        try {
            int days = 7; // 默认7天
            if ("30d".equals(period)) {
                days = 30;
            } else if ("90d".equals(period)) {
                days = 90;
            }

            // 使用Map来汇总各个支付类型的成功率数据
            Map<String, Map<String, Object>> dateDataMap = new HashMap<>();

            // 获取任务支付成功率统计
            List<Map<String, Object>> taskSuccessRateData = taskPaymentMapper.getPaymentSuccessRateStatistics(days);
            if (taskSuccessRateData != null) {
                for (Map<String, Object> data : taskSuccessRateData) {
                    String date = data.get("date").toString();
                    Map<String, Object> dayData = dateDataMap.getOrDefault(date, new HashMap<>());
                    dayData.put("date", data.get("date"));
                    dayData.put("totalCount", getIntValue(dayData.get("totalCount")) + getIntValue(data.get("totalCount")));
                    dayData.put("successCount", getIntValue(dayData.get("successCount")) + getIntValue(data.get("successCount")));
                    dateDataMap.put(date, dayData);
                }
            }

            // 获取商城支付成功率统计
            List<Map<String, Object>> mallSuccessRateData = mallPaymentMapper.getPaymentSuccessRateStatistics(days);
            if (mallSuccessRateData != null) {
                for (Map<String, Object> data : mallSuccessRateData) {
                    String date = data.get("date").toString();
                    Map<String, Object> dayData = dateDataMap.getOrDefault(date, new HashMap<>());
                    dayData.put("date", data.get("date"));
                    dayData.put("totalCount", getIntValue(dayData.get("totalCount")) + getIntValue(data.get("totalCount")));
                    dayData.put("successCount", getIntValue(dayData.get("successCount")) + getIntValue(data.get("successCount")));
                    dateDataMap.put(date, dayData);
                }
            }

            // 获取线下支付成功率统计
            List<Map<String, Object>> offlineSuccessRateData = offlinePaymentMapper.getPaymentSuccessRateStatistics(days);
            if (offlineSuccessRateData != null) {
                for (Map<String, Object> data : offlineSuccessRateData) {
                    String date = data.get("date").toString();
                    Map<String, Object> dayData = dateDataMap.getOrDefault(date, new HashMap<>());
                    dayData.put("date", data.get("date"));
                    dayData.put("totalCount", getIntValue(dayData.get("totalCount")) + getIntValue(data.get("totalCount")));
                    dayData.put("successCount", getIntValue(dayData.get("successCount")) + getIntValue(data.get("successCount")));
                    dateDataMap.put(date, dayData);
                }
            }

            // 计算成功率并转换为列表
            for (Map<String, Object> dayData : dateDataMap.values()) {
                int totalCount = getIntValue(dayData.get("totalCount"));
                int successCount = getIntValue(dayData.get("successCount"));
                BigDecimal successRate = totalCount > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                dayData.put("successRate", successRate);
                successRateData.add(dayData);
            }

            // 按日期排序
            successRateData.sort((a, b) -> b.get("date").toString().compareTo(a.get("date").toString()));

        } catch (Exception e) {
            log.error("获取支付成功率统计异常", e);
        }

        return successRateData;
    }

    @Override
    public List<Map<String, Object>> getPaymentAmountDistribution() {
        List<Map<String, Object>> distributionData = new ArrayList<>();

        try {
            // 使用Map来汇总各个支付类型的金额分布数据
            Map<String, Map<String, Object>> rangeDataMap = new HashMap<>();

            // 获取任务支付金额分布
            List<Map<String, Object>> taskDistributionData = taskPaymentMapper.getPaymentAmountDistribution();
            if (taskDistributionData != null) {
                for (Map<String, Object> data : taskDistributionData) {
                    String range = data.get("amountRange").toString();
                    Map<String, Object> rangeData = rangeDataMap.getOrDefault(range, new HashMap<>());
                    rangeData.put("amountRange", range);
                    rangeData.put("count", getIntValue(rangeData.get("count")) + getIntValue(data.get("count")));
                    rangeData.put("totalAmount", getBigDecimalValue(rangeData.get("totalAmount")).add(getBigDecimalValue(data.get("totalAmount"))));
                    rangeDataMap.put(range, rangeData);
                }
            }

            // 获取商城支付金额分布
            List<Map<String, Object>> mallDistributionData = mallPaymentMapper.getPaymentAmountDistribution();
            if (mallDistributionData != null) {
                for (Map<String, Object> data : mallDistributionData) {
                    String range = data.get("amountRange").toString();
                    Map<String, Object> rangeData = rangeDataMap.getOrDefault(range, new HashMap<>());
                    rangeData.put("amountRange", range);
                    rangeData.put("count", getIntValue(rangeData.get("count")) + getIntValue(data.get("count")));
                    rangeData.put("totalAmount", getBigDecimalValue(rangeData.get("totalAmount")).add(getBigDecimalValue(data.get("totalAmount"))));
                    rangeDataMap.put(range, rangeData);
                }
            }

            // 获取线下支付金额分布
            List<Map<String, Object>> offlineDistributionData = offlinePaymentMapper.getPaymentAmountDistribution();
            if (offlineDistributionData != null) {
                for (Map<String, Object> data : offlineDistributionData) {
                    String range = data.get("amountRange").toString();
                    Map<String, Object> rangeData = rangeDataMap.getOrDefault(range, new HashMap<>());
                    rangeData.put("amountRange", range);
                    rangeData.put("count", getIntValue(rangeData.get("count")) + getIntValue(data.get("count")));
                    rangeData.put("totalAmount", getBigDecimalValue(rangeData.get("totalAmount")).add(getBigDecimalValue(data.get("totalAmount"))));
                    rangeDataMap.put(range, rangeData);
                }
            }

            // 转换为列表并计算百分比
            int totalCount = rangeDataMap.values().stream().mapToInt(data -> getIntValue(data.get("count"))).sum();
            for (Map<String, Object> rangeData : rangeDataMap.values()) {
                int count = getIntValue(rangeData.get("count"));
                BigDecimal percentage = totalCount > 0 ?
                    BigDecimal.valueOf(count * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                rangeData.put("percentage", percentage);
                distributionData.add(rangeData);
            }

        } catch (Exception e) {
            log.error("获取支付金额分布统计异常", e);
        }

        return distributionData;
    }

    @Override
    public Map<String, Object> getUserPaymentBehaviorAnalysis() {
        Map<String, Object> behaviorAnalysis = new HashMap<>();

        try {
            int totalUsers = 0;
            BigDecimal avgPayAmount = BigDecimal.ZERO;
            BigDecimal maxPayAmount = BigDecimal.ZERO;
            BigDecimal minPayAmount = BigDecimal.ZERO;
            BigDecimal avgPayCountPerUser = BigDecimal.ZERO;

            // 汇总任务支付用户行为分析
            Map<String, Object> taskBehaviorAnalysis = taskPaymentMapper.getUserPaymentBehaviorAnalysis();
            if (taskBehaviorAnalysis != null) {
                totalUsers += getIntValue(taskBehaviorAnalysis.get("totalUsers"));
                avgPayAmount = avgPayAmount.add(getBigDecimalValue(taskBehaviorAnalysis.get("avgPayAmount")));
                BigDecimal taskMaxAmount = getBigDecimalValue(taskBehaviorAnalysis.get("maxPayAmount"));
                if (taskMaxAmount.compareTo(maxPayAmount) > 0) {
                    maxPayAmount = taskMaxAmount;
                }
                BigDecimal taskMinAmount = getBigDecimalValue(taskBehaviorAnalysis.get("minPayAmount"));
                if (minPayAmount.equals(BigDecimal.ZERO) || (taskMinAmount.compareTo(BigDecimal.ZERO) > 0 && taskMinAmount.compareTo(minPayAmount) < 0)) {
                    minPayAmount = taskMinAmount;
                }
                avgPayCountPerUser = avgPayCountPerUser.add(getBigDecimalValue(taskBehaviorAnalysis.get("avgPayCountPerUser")));
            }

            // 汇总商城支付用户行为分析
            Map<String, Object> mallBehaviorAnalysis = mallPaymentMapper.getUserPaymentBehaviorAnalysis();
            if (mallBehaviorAnalysis != null) {
                totalUsers += getIntValue(mallBehaviorAnalysis.get("totalUsers"));
                avgPayAmount = avgPayAmount.add(getBigDecimalValue(mallBehaviorAnalysis.get("avgPayAmount")));
                BigDecimal mallMaxAmount = getBigDecimalValue(mallBehaviorAnalysis.get("maxPayAmount"));
                if (mallMaxAmount.compareTo(maxPayAmount) > 0) {
                    maxPayAmount = mallMaxAmount;
                }
                BigDecimal mallMinAmount = getBigDecimalValue(mallBehaviorAnalysis.get("minPayAmount"));
                if (minPayAmount.equals(BigDecimal.ZERO) || (mallMinAmount.compareTo(BigDecimal.ZERO) > 0 && mallMinAmount.compareTo(minPayAmount) < 0)) {
                    minPayAmount = mallMinAmount;
                }
                avgPayCountPerUser = avgPayCountPerUser.add(getBigDecimalValue(mallBehaviorAnalysis.get("avgPayCountPerUser")));
            }

            // 汇总线下支付用户行为分析（这里是商家行为分析）
            Map<String, Object> offlineBehaviorAnalysis = offlinePaymentMapper.getUserPaymentBehaviorAnalysis();
            if (offlineBehaviorAnalysis != null) {
                totalUsers += getIntValue(offlineBehaviorAnalysis.get("totalMerchants")); // 线下支付统计的是商家
                avgPayAmount = avgPayAmount.add(getBigDecimalValue(offlineBehaviorAnalysis.get("avgPayAmount")));
                BigDecimal offlineMaxAmount = getBigDecimalValue(offlineBehaviorAnalysis.get("maxPayAmount"));
                if (offlineMaxAmount.compareTo(maxPayAmount) > 0) {
                    maxPayAmount = offlineMaxAmount;
                }
                BigDecimal offlineMinAmount = getBigDecimalValue(offlineBehaviorAnalysis.get("minPayAmount"));
                if (minPayAmount.equals(BigDecimal.ZERO) || (offlineMinAmount.compareTo(BigDecimal.ZERO) > 0 && offlineMinAmount.compareTo(minPayAmount) < 0)) {
                    minPayAmount = offlineMinAmount;
                }
                avgPayCountPerUser = avgPayCountPerUser.add(getBigDecimalValue(offlineBehaviorAnalysis.get("avgPayCountPerMerchant")));
            }

            // 计算平均值
            if (totalUsers > 0) {
                avgPayAmount = avgPayAmount.divide(BigDecimal.valueOf(3), 2, RoundingMode.HALF_UP); // 3个业务类型的平均
                avgPayCountPerUser = avgPayCountPerUser.divide(BigDecimal.valueOf(3), 2, RoundingMode.HALF_UP);
            }

            behaviorAnalysis.put("totalUsers", totalUsers);
            behaviorAnalysis.put("avgPayAmount", avgPayAmount);
            behaviorAnalysis.put("maxPayAmount", maxPayAmount);
            behaviorAnalysis.put("minPayAmount", minPayAmount);
            behaviorAnalysis.put("avgPayCountPerUser", avgPayCountPerUser);

        } catch (Exception e) {
            log.error("获取用户支付行为分析异常", e);
            behaviorAnalysis.put("totalUsers", 0);
            behaviorAnalysis.put("avgPayAmount", BigDecimal.ZERO);
            behaviorAnalysis.put("maxPayAmount", BigDecimal.ZERO);
            behaviorAnalysis.put("minPayAmount", BigDecimal.ZERO);
            behaviorAnalysis.put("avgPayCountPerUser", BigDecimal.ZERO);
        }

        return behaviorAnalysis;
    }

    @Override
    public List<Map<String, Object>> getPaymentTimeAnalysis() {
        List<Map<String, Object>> timeAnalysisData = new ArrayList<>();

        try {
            // 使用Map来汇总各个支付类型的时段数据
            Map<Integer, Map<String, Object>> hourDataMap = new HashMap<>();

            // 初始化24小时数据
            for (int hour = 0; hour < 24; hour++) {
                Map<String, Object> hourData = new HashMap<>();
                hourData.put("hour", hour);
                hourData.put("count", 0);
                hourData.put("amount", BigDecimal.ZERO);
                hourDataMap.put(hour, hourData);
            }

            // 获取任务支付时段分析
            List<Map<String, Object>> taskTimeAnalysis = taskPaymentMapper.getPaymentTimeAnalysis();
            if (taskTimeAnalysis != null) {
                for (Map<String, Object> data : taskTimeAnalysis) {
                    Integer hour = getIntValue(data.get("hour"));
                    Map<String, Object> hourData = hourDataMap.get(hour);
                    if (hourData != null) {
                        hourData.put("count", getIntValue(hourData.get("count")) + getIntValue(data.get("count")));
                        hourData.put("amount", getBigDecimalValue(hourData.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    }
                }
            }

            // 获取商城支付时段分析
            List<Map<String, Object>> mallTimeAnalysis = mallPaymentMapper.getPaymentTimeAnalysis();
            if (mallTimeAnalysis != null) {
                for (Map<String, Object> data : mallTimeAnalysis) {
                    Integer hour = getIntValue(data.get("hour"));
                    Map<String, Object> hourData = hourDataMap.get(hour);
                    if (hourData != null) {
                        hourData.put("count", getIntValue(hourData.get("count")) + getIntValue(data.get("count")));
                        hourData.put("amount", getBigDecimalValue(hourData.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    }
                }
            }

            // 获取线下支付时段分析
            List<Map<String, Object>> offlineTimeAnalysis = offlinePaymentMapper.getPaymentTimeAnalysis();
            if (offlineTimeAnalysis != null) {
                for (Map<String, Object> data : offlineTimeAnalysis) {
                    Integer hour = getIntValue(data.get("hour"));
                    Map<String, Object> hourData = hourDataMap.get(hour);
                    if (hourData != null) {
                        hourData.put("count", getIntValue(hourData.get("count")) + getIntValue(data.get("count")));
                        hourData.put("amount", getBigDecimalValue(hourData.get("amount")).add(getBigDecimalValue(data.get("amount"))));
                    }
                }
            }

            // 转换为列表并按小时排序
            timeAnalysisData.addAll(hourDataMap.values());
            timeAnalysisData.sort((a, b) -> Integer.compare(getIntValue(a.get("hour")), getIntValue(b.get("hour"))));

        } catch (Exception e) {
            log.error("获取支付时段分析异常", e);
        }

        return timeAnalysisData;
    }

    @Override
    public Map<String, Object> getRefundStatistics() {
        Map<String, Object> refundStats = new HashMap<>();

        try {
            int totalRefundCount = 0;
            BigDecimal totalRefundAmount = BigDecimal.ZERO;
            int todayRefundCount = 0;
            BigDecimal todayRefundAmount = BigDecimal.ZERO;

            // 汇总任务支付退款统计
            Map<String, Object> taskRefundStats = taskPaymentMapper.getRefundStatistics();
            if (taskRefundStats != null) {
                totalRefundCount += getIntValue(taskRefundStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(taskRefundStats.get("refundAmount")));
                todayRefundCount += getIntValue(taskRefundStats.get("todayRefundCount"));
                todayRefundAmount = todayRefundAmount.add(getBigDecimalValue(taskRefundStats.get("todayRefundAmount")));
            }

            // 汇总商城支付退款统计
            Map<String, Object> mallRefundStats = mallPaymentMapper.getRefundStatistics();
            if (mallRefundStats != null) {
                totalRefundCount += getIntValue(mallRefundStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(mallRefundStats.get("refundAmount")));
                todayRefundCount += getIntValue(mallRefundStats.get("todayRefundCount"));
                todayRefundAmount = todayRefundAmount.add(getBigDecimalValue(mallRefundStats.get("todayRefundAmount")));
            }

            // 汇总线下支付退款统计
            Map<String, Object> offlineRefundStats = offlinePaymentMapper.getRefundStatistics();
            if (offlineRefundStats != null) {
                totalRefundCount += getIntValue(offlineRefundStats.get("refundCount"));
                totalRefundAmount = totalRefundAmount.add(getBigDecimalValue(offlineRefundStats.get("refundAmount")));
                todayRefundCount += getIntValue(offlineRefundStats.get("todayRefundCount"));
                todayRefundAmount = todayRefundAmount.add(getBigDecimalValue(offlineRefundStats.get("todayRefundAmount")));
            }

            // 计算退款率
            Map<String, Object> overviewStats = getPaymentOverview();
            int totalSuccessCount = getIntValue(overviewStats.get("successCount"));
            BigDecimal refundRate = totalSuccessCount > 0 ?
                BigDecimal.valueOf(totalRefundCount * 100.0 / totalSuccessCount).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

            refundStats.put("refundCount", totalRefundCount);
            refundStats.put("refundAmount", totalRefundAmount);
            refundStats.put("todayRefundCount", todayRefundCount);
            refundStats.put("todayRefundAmount", todayRefundAmount);
            refundStats.put("refundRate", refundRate);

        } catch (Exception e) {
            log.error("获取退款统计异常", e);
            refundStats.put("refundCount", 0);
            refundStats.put("refundAmount", BigDecimal.ZERO);
            refundStats.put("todayRefundCount", 0);
            refundStats.put("todayRefundAmount", BigDecimal.ZERO);
            refundStats.put("refundRate", BigDecimal.ZERO);
        }

        return refundStats;
    }

    @Override
    public List<Map<String, Object>> getPaymentPlatformComparison() {
        List<Map<String, Object>> platformData = new ArrayList<>();

        try {
            // 创建平台对比数据
            Map<String, Object> taskPlatform = new HashMap<>();
            taskPlatform.put("platform", "任务支付");
            taskPlatform.put("platformCode", "task");

            Map<String, Object> mallPlatform = new HashMap<>();
            mallPlatform.put("platform", "商城支付");
            mallPlatform.put("platformCode", "mall");

            Map<String, Object> offlinePlatform = new HashMap<>();
            offlinePlatform.put("platform", "线下支付");
            offlinePlatform.put("platformCode", "offline");

            // 获取各平台统计数据
            Map<String, Object> taskStats = taskPaymentMapper.getPaymentOverviewStatistics();
            if (taskStats != null) {
                taskPlatform.put("totalCount", getIntValue(taskStats.get("totalCount")));
                taskPlatform.put("totalAmount", getBigDecimalValue(taskStats.get("totalAmount")));
                taskPlatform.put("successCount", getIntValue(taskStats.get("successCount")));
                taskPlatform.put("successAmount", getBigDecimalValue(taskStats.get("successAmount")));

                int totalCount = getIntValue(taskStats.get("totalCount"));
                int successCount = getIntValue(taskStats.get("successCount"));
                BigDecimal successRate = totalCount > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                taskPlatform.put("successRate", successRate);
            }

            Map<String, Object> mallStats = mallPaymentMapper.getPaymentOverviewStatistics();
            if (mallStats != null) {
                mallPlatform.put("totalCount", getIntValue(mallStats.get("totalCount")));
                mallPlatform.put("totalAmount", getBigDecimalValue(mallStats.get("totalAmount")));
                mallPlatform.put("successCount", getIntValue(mallStats.get("successCount")));
                mallPlatform.put("successAmount", getBigDecimalValue(mallStats.get("successAmount")));

                int totalCount = getIntValue(mallStats.get("totalCount"));
                int successCount = getIntValue(mallStats.get("successCount"));
                BigDecimal successRate = totalCount > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                mallPlatform.put("successRate", successRate);
            }

            Map<String, Object> offlineStats = offlinePaymentMapper.getPaymentOverviewStatistics();
            if (offlineStats != null) {
                offlinePlatform.put("totalCount", getIntValue(offlineStats.get("totalCount")));
                offlinePlatform.put("totalAmount", getBigDecimalValue(offlineStats.get("totalAmount")));
                offlinePlatform.put("successCount", getIntValue(offlineStats.get("successCount")));
                offlinePlatform.put("successAmount", getBigDecimalValue(offlineStats.get("successAmount")));

                int totalCount = getIntValue(offlineStats.get("totalCount"));
                int successCount = getIntValue(offlineStats.get("successCount"));
                BigDecimal successRate = totalCount > 0 ?
                    BigDecimal.valueOf(successCount * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                offlinePlatform.put("successRate", successRate);
            }

            platformData.add(taskPlatform);
            platformData.add(mallPlatform);
            platformData.add(offlinePlatform);

            // 按交易量排序
            platformData.sort((a, b) -> Integer.compare(getIntValue(b.get("totalCount")), getIntValue(a.get("totalCount"))));

        } catch (Exception e) {
            log.error("获取支付平台对比统计异常", e);
        }

        return platformData;
    }



    // ==================== 辅助方法 ====================

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全获取Integer值
     */
    private int getIntValue(Object value) {
        if (value == null) {
            return 0;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 安全获取Long值
     */
    private long getLongValue(Object value) {
        if (value == null) {
            return 0L;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (Exception e) {
            return 0L;
        }
    }


}
