package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 支付回调日志对象 payment_callback_log
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class PaymentCallbackLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 回调日志ID */
    private Long callbackId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 业务类型（task任务支付 mall商城支付 offline线下支付） */
    @Excel(name = "业务类型", readConverterExp = "task=任务支付,mall=商城支付,offline=线下支付")
    private String businessType;

    /** 支付方式（1支付宝 2微信 3余额） */
    @Excel(name = "支付方式", readConverterExp = "1=支付宝,2=微信,3=余额")
    private String payType;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmount;

    /** 回调状态（0失败 1成功） */
    @Excel(name = "回调状态", readConverterExp = "0=失败,1=成功")
    private String callbackStatus;

    /** 回调参数（JSON格式） */
    @Excel(name = "回调参数")
    private String callbackParams;

    /** 回调时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回调时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date callbackTime;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMsg;

    public void setCallbackId(Long callbackId) 
    {
        this.callbackId = callbackId;
    }

    public Long getCallbackId() 
    {
        return callbackId;
    }

    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }

    public void setBusinessType(String businessType) 
    {
        this.businessType = businessType;
    }

    public String getBusinessType() 
    {
        return businessType;
    }

    public void setPayType(String payType) 
    {
        this.payType = payType;
    }

    public String getPayType() 
    {
        return payType;
    }

    public void setPayAmount(BigDecimal payAmount) 
    {
        this.payAmount = payAmount;
    }

    public BigDecimal getPayAmount() 
    {
        return payAmount;
    }

    public void setCallbackStatus(String callbackStatus) 
    {
        this.callbackStatus = callbackStatus;
    }

    public String getCallbackStatus() 
    {
        return callbackStatus;
    }

    public void setCallbackParams(String callbackParams) 
    {
        this.callbackParams = callbackParams;
    }

    public String getCallbackParams() 
    {
        return callbackParams;
    }

    public void setCallbackTime(Date callbackTime) 
    {
        this.callbackTime = callbackTime;
    }

    public Date getCallbackTime() 
    {
        return callbackTime;
    }

    public void setRetryCount(Integer retryCount) 
    {
        this.retryCount = retryCount;
    }

    public Integer getRetryCount() 
    {
        return retryCount;
    }

    public void setErrorMsg(String errorMsg) 
    {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() 
    {
        return errorMsg;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("callbackId", getCallbackId())
            .append("orderNo", getOrderNo())
            .append("businessType", getBusinessType())
            .append("payType", getPayType())
            .append("payAmount", getPayAmount())
            .append("callbackStatus", getCallbackStatus())
            .append("callbackParams", getCallbackParams())
            .append("callbackTime", getCallbackTime())
            .append("retryCount", getRetryCount())
            .append("errorMsg", getErrorMsg())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
