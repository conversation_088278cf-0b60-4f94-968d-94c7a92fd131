package com.ruoyi.fuguang.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.config.AlipayConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.mapper.MallPaymentMapper;
import com.ruoyi.fuguang.domain.MallPayment;
import com.ruoyi.fuguang.domain.MallOrder;
import com.ruoyi.fuguang.service.IMallPaymentService;
import com.ruoyi.fuguang.service.IMallOrderService;
import com.ruoyi.fuguang.service.IAlipayService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 支付记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MallPaymentServiceImpl implements IMallPaymentService 
{
    private static final Logger log = LoggerFactory.getLogger(MallPaymentServiceImpl.class);

    @Autowired
    private MallPaymentMapper mallPaymentMapper;

    @Autowired
    private IMallOrderService mallOrderService;

    @Autowired
    private AlipayConfig alipayConfig;

    @Autowired
    private IAlipayService alipayService;

    /**
     * 查询支付记录
     * 
     * @param paymentId 支付记录主键
     * @return 支付记录
     */
    @Override
    public MallPayment selectMallPaymentByPaymentId(Long paymentId)
    {
        return mallPaymentMapper.selectMallPaymentByPaymentId(paymentId);
    }

    /**
     * 查询支付记录列表
     * 
     * @param mallPayment 支付记录
     * @return 支付记录
     */
    @Override
    public List<MallPayment> selectMallPaymentList(MallPayment mallPayment)
    {
        return mallPaymentMapper.selectMallPaymentList(mallPayment);
    }

    /**
     * 新增支付记录
     * 
     * @param mallPayment 支付记录
     * @return 结果
     */
    @Override
    public int insertMallPayment(MallPayment mallPayment)
    {
        mallPayment.setCreateTime(DateUtils.getNowDate());
        return mallPaymentMapper.insertMallPayment(mallPayment);
    }

    /**
     * 修改支付记录
     * 
     * @param mallPayment 支付记录
     * @return 结果
     */
    @Override
    public int updateMallPayment(MallPayment mallPayment)
    {
        mallPayment.setUpdateTime(DateUtils.getNowDate());
        return mallPaymentMapper.updateMallPayment(mallPayment);
    }

    /**
     * 批量删除支付记录
     * 
     * @param paymentIds 需要删除的支付记录主键
     * @return 结果
     */
    @Override
    public int deleteMallPaymentByPaymentIds(Long[] paymentIds)
    {
        return mallPaymentMapper.deleteMallPaymentByPaymentIds(paymentIds);
    }

    /**
     * 删除支付记录信息
     * 
     * @param paymentId 支付记录主键
     * @return 结果
     */
    @Override
    public int deleteMallPaymentByPaymentId(Long paymentId)
    {
        return mallPaymentMapper.deleteMallPaymentByPaymentId(paymentId);
    }

    /**
     * 根据订单ID查询支付记录
     * 
     * @param orderId 订单ID
     * @return 支付记录
     */
    @Override
    public MallPayment selectPaymentByOrderId(Long orderId)
    {
        return mallPaymentMapper.selectPaymentByOrderId(orderId);
    }

    /**
     * 根据订单号查询支付记录
     * 
     * @param orderNo 订单号
     * @return 支付记录
     */
    @Override
    public MallPayment selectPaymentByOrderNo(String orderNo)
    {
        return mallPaymentMapper.selectPaymentByOrderNo(orderNo);
    }

    /**
     * 创建支付记录
     * 
     * @param order 订单信息
     * @param payType 支付方式
     * @return 支付记录
     */
    @Override
    public MallPayment createPayment(MallOrder order, String payType)
    {
        MallPayment payment = new MallPayment();
        payment.setOrderId(order.getOrderId());
        payment.setOrderNo(order.getOrderNo());
        payment.setUserId(order.getUserId());
        payment.setPayAmount(order.getPayAmount());
        payment.setPayType(payType);
        payment.setPayStatus("0"); // 待支付
        payment.setCreateTime(new Date());
        
        mallPaymentMapper.insertMallPayment(payment);
        return payment;
    }



    /**
     * 处理支付宝支付回调
     * 
     * @param params 回调参数
     * @return 处理结果
     */
    @Override
    public boolean handleAlipayCallback(Map<String, String> params)
    {
        try {
            // 验证签名
            boolean signVerified = AlipaySignature.rsaCheckV1(
                params, 
                alipayConfig.getPublicKey(), 
                alipayConfig.getCharset(), 
                alipayConfig.getSignType()
            );
            
            if (!signVerified) {
                log.error("支付宝回调签名验证失败");
                return false;
            }
            
            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                // 支付成功
                return paymentSuccess(outTradeNo, tradeNo, "1") > 0;
            } else {
                log.warn("支付宝回调状态异常：{}", tradeStatus);
                return false;
            }
        } catch (Exception e) {
            log.error("处理支付宝回调异常", e);
            return false;
        }
    }

    /**
     * 支付成功处理
     * 
     * @param orderNo 订单号
     * @param tradeNo 第三方交易号
     * @param payType 支付方式
     * @return 结果
     */
    @Override
    public int paymentSuccess(String orderNo, String tradeNo, String payType)
    {
        try {
            // 更新支付记录
            mallPaymentMapper.updatePaymentStatusByOrderNo(orderNo, "1", tradeNo);
            
            // 更新订单状态
            mallOrderService.paymentSuccess(orderNo, tradeNo);
            
            return 1;
        } catch (Exception e) {
            log.error("支付成功处理异常", e);
            return 0;
        }
    }

    /**
     * 支付失败处理
     * 
     * @param orderNo 订单号
     * @return 结果
     */
    @Override
    public int paymentFailed(String orderNo)
    {
        try {
            // 更新支付记录
            mallPaymentMapper.updatePaymentStatusByOrderNo(orderNo, "2", null);
            
            // 更新订单状态
            mallOrderService.paymentFailed(orderNo);
            
            return 1;
        } catch (Exception e) {
            log.error("支付失败处理异常", e);
            return 0;
        }
    }

    /**
     * 查询支付状态
     * 
     * @param orderNo 订单号
     * @return 支付状态
     */
    @Override
    public String getPaymentStatus(String orderNo)
    {
        MallPayment payment = mallPaymentMapper.selectPaymentByOrderNo(orderNo);
        return payment != null ? payment.getPayStatus() : "0";
    }

    /**
     * 创建商城支付订单（参考任务支付逻辑）
     *
     * @param order 订单信息
     * @param payType 支付方式
     * @return 支付记录
     */
    @Override
    @Transactional
    public MallPayment createMallPayOrder(MallOrder order, String payType)
    {
        try {
            // 检查是否已存在支付记录
            MallPayment existPayment = selectPaymentByOrderId(order.getOrderId());
            if (StringUtils.isNotNull(existPayment)) {
                if(!existPayment.getPayStatus().equals("0")){
                    throw new ServiceException("订单无法发起支付");
                }
                return existPayment;
            }
            // 生成订单号（如果订单还没有订单号）
            String orderNo = order.getOrderNo();
            if (StringUtils.isEmpty(orderNo)) {
                orderNo = "MALL_" + System.currentTimeMillis() + "_" + order.getOrderId();
                order.setOrderNo(orderNo);
                mallOrderService.updateMallOrder(order);
            }
            // 创建支付记录
            MallPayment payment = new MallPayment();
            payment.setOrderId(order.getOrderId());
            payment.setOrderNo(orderNo);
            payment.setUserId(order.getUserId());
            payment.setPayAmount(order.getPayAmount());
            payment.setPayType(payType);
            payment.setPayStatus("0"); // 待支付
            // 根据支付方式创建支付订单
            switch (payType) {
                case "1":
                    // 支付宝支付
                    Map<String, Object> payResult = alipayService.createAppPayOrder(
                            orderNo,
                            order.getPayAmount(),
                            "商城订单-" + orderNo,
                            "商城订单支付，订单ID：" + order.getOrderId()
                    );
                    if ((Boolean) payResult.get("success")) {
                        payment.setPayOrderString((String) payResult.get("orderString"));
                        break;
                    } else {
                        throw new ServiceException("创建支付订单失败：" + payResult.get("errorMsg"));
                    }
                case "2":
                case "3":
                    throw new ServiceException("暂未开放");
                default:
                    throw new ServiceException("暂不支持");
            }
            insertMallPayment(payment);
            return payment;
        } catch (Exception e) {
            log.error("创建商城支付订单异常，订单ID：{}", order.getOrderId(), e);
            throw new RuntimeException("创建支付订单异常：" + e.getMessage());
        }
    }
}
