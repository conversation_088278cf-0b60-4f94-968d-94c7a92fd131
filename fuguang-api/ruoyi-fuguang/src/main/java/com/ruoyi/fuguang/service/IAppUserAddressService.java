package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.AppUserAddress;

/**
 * APP用户地址Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface IAppUserAddressService 
{
    /**
     * 查询APP用户地址
     * 
     * @param addressId APP用户地址主键
     * @return APP用户地址
     */
    public AppUserAddress selectAppUserAddressByAddressId(Long addressId);

    /**
     * 查询APP用户地址列表
     * 
     * @param appUserAddress APP用户地址
     * @return APP用户地址集合
     */
    public List<AppUserAddress> selectAppUserAddressList(AppUserAddress appUserAddress);

    /**
     * 根据用户ID查询地址列表
     * 
     * @param userId 用户ID
     * @return APP用户地址集合
     */
    public List<AppUserAddress> selectAppUserAddressListByUserId(Long userId);

    /**
     * 查询用户默认地址
     * 
     * @param userId 用户ID
     * @return APP用户地址
     */
    public AppUserAddress selectDefaultAddressByUserId(Long userId);

    /**
     * 新增APP用户地址
     * 
     * @param appUserAddress APP用户地址
     * @return 结果
     */
    public int insertAppUserAddress(AppUserAddress appUserAddress);

    /**
     * 修改APP用户地址
     * 
     * @param appUserAddress APP用户地址
     * @return 结果
     */
    public int updateAppUserAddress(AppUserAddress appUserAddress);

    /**
     * 批量删除APP用户地址
     * 
     * @param addressIds 需要删除的APP用户地址主键集合
     * @return 结果
     */
    public int deleteAppUserAddressByAddressIds(Long[] addressIds);

    /**
     * 删除APP用户地址信息
     * 
     * @param addressId APP用户地址主键
     * @return 结果
     */
    public int deleteAppUserAddressByAddressId(Long addressId);

    /**
     * 设置默认地址
     * 
     * @param addressId 地址ID
     * @param userId 用户ID
     * @return 结果
     */
    public int setDefaultAddress(Long addressId, Long userId);


    /**
     * 校验地址数据是否有效
     * 
     * @param appUserAddress APP用户地址
     * @return 结果
     */
    public String validateAddressData(AppUserAddress appUserAddress);

    /**
     * 构建完整地址
     * 
     * @param appUserAddress APP用户地址
     * @return 完整地址
     */
    public String buildFullAddress(AppUserAddress appUserAddress);
}
