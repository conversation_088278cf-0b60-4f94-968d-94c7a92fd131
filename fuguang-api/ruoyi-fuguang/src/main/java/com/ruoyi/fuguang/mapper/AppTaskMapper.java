package com.ruoyi.fuguang.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.fuguang.domain.AppTask;

/**
 * APP任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface AppTaskMapper 
{
    /**
     * 查询APP任务
     * 
     * @param taskId APP任务主键
     * @return APP任务
     */
    public AppTask selectAppTaskByTaskId(Long taskId);

    /**
     * 查询APP任务列表
     * 
     * @param appTask APP任务
     * @return APP任务集合
     */
    public List<AppTask> selectAppTaskList(AppTask appTask);

    /**
     * 新增APP任务
     * 
     * @param appTask APP任务
     * @return 结果
     */
    public int insertAppTask(AppTask appTask);

    /**
     * 修改APP任务
     * 
     * @param appTask APP任务
     * @return 结果
     */
    public int updateAppTask(AppTask appTask);

    /**
     * 删除APP任务
     * 
     * @param taskId APP任务主键
     * @return 结果
     */
    public int deleteAppTaskByTaskId(Long taskId);

    /**
     * 批量删除APP任务
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppTaskByTaskIds(Long[] taskIds);

    /**
     * 查询热门任务列表
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param limit 限制数量
     * @return 任务列表
     */
    public List<AppTask> selectHotTaskList(@Param("longitude") String longitude, 
                                          @Param("latitude") String latitude, 
                                          @Param("limit") Integer limit);

    /**
     * 增加任务浏览次数
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    public int increaseViewCount(Long taskId);

    /**
     * 接取任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param userName 用户昵称
     * @return 结果
     */
    public int acceptTask(@Param("taskId") Long taskId, 
                         @Param("userId") Long userId, 
                         @Param("userName") String userName);

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    public int completeTask(Long taskId);

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    public int cancelTask(Long taskId);

    /**
     * 更新任务状态为待接取
     *
     * @param taskId 任务ID
     * @return 结果
     */
    public int updateTaskStatusToWaitingAccept(Long taskId);

    /**
     * 终止任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    public int terminateTask(Long taskId);

    /**
     * 查询用户发布的任务
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    public List<AppTask> selectTasksByPublisher(Long userId);

    /**
     * 查询用户接取的任务
     *
     * @param userId 用户ID
     * @return 任务列表
     */
    public List<AppTask> selectTasksByReceiver(Long userId);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param taskStatus 任务状态
     * @return 结果
     */
    public int updateTaskStatus(@Param("taskId") Long taskId, @Param("taskStatus") String taskStatus);
}
