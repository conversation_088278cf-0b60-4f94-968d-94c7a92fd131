package com.ruoyi.fuguang.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.fuguang.domain.MallPayment;
import com.ruoyi.fuguang.domain.MallOrder;

/**
 * 支付记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IMallPaymentService 
{
    /**
     * 查询支付记录
     * 
     * @param paymentId 支付记录主键
     * @return 支付记录
     */
    public MallPayment selectMallPaymentByPaymentId(Long paymentId);

    /**
     * 查询支付记录列表
     * 
     * @param mallPayment 支付记录
     * @return 支付记录集合
     */
    public List<MallPayment> selectMallPaymentList(MallPayment mallPayment);

    /**
     * 新增支付记录
     * 
     * @param mallPayment 支付记录
     * @return 结果
     */
    public int insertMallPayment(MallPayment mallPayment);

    /**
     * 修改支付记录
     * 
     * @param mallPayment 支付记录
     * @return 结果
     */
    public int updateMallPayment(MallPayment mallPayment);

    /**
     * 批量删除支付记录
     * 
     * @param paymentIds 需要删除的支付记录主键集合
     * @return 结果
     */
    public int deleteMallPaymentByPaymentIds(Long[] paymentIds);

    /**
     * 删除支付记录信息
     * 
     * @param paymentId 支付记录主键
     * @return 结果
     */
    public int deleteMallPaymentByPaymentId(Long paymentId);

    /**
     * 根据订单ID查询支付记录
     * 
     * @param orderId 订单ID
     * @return 支付记录
     */
    public MallPayment selectPaymentByOrderId(Long orderId);

    /**
     * 根据订单号查询支付记录
     * 
     * @param orderNo 订单号
     * @return 支付记录
     */
    public MallPayment selectPaymentByOrderNo(String orderNo);

    /**
     * 创建支付记录
     * 
     * @param order 订单信息
     * @param payType 支付方式
     * @return 支付记录
     */
    public MallPayment createPayment(MallOrder order, String payType);


    /**
     * 处理支付宝支付回调
     * 
     * @param params 回调参数
     * @return 处理结果
     */
    public boolean handleAlipayCallback(Map<String, String> params);

    /**
     * 支付成功处理
     * 
     * @param orderNo 订单号
     * @param tradeNo 第三方交易号
     * @param payType 支付方式
     * @return 结果
     */
    public int paymentSuccess(String orderNo, String tradeNo, String payType);

    /**
     * 支付失败处理
     * 
     * @param orderNo 订单号
     * @return 结果
     */
    public int paymentFailed(String orderNo);

    /**
     * 查询支付状态
     *
     * @param orderNo 订单号
     * @return 支付状态
     */
    public String getPaymentStatus(String orderNo);

    /**
     * 创建商城支付订单（参考任务支付逻辑）
     *
     * @param order 订单信息
     * @param payType 支付方式
     * @return 支付记录
     */
    public MallPayment createMallPayOrder(MallOrder order, String payType);
}
