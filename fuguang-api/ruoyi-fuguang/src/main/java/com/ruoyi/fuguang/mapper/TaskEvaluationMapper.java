package com.ruoyi.fuguang.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.fuguang.domain.TaskEvaluation;

/**
 * 任务评价Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
public interface TaskEvaluationMapper 
{
    /**
     * 查询任务评价
     * 
     * @param evaluationId 任务评价主键
     * @return 任务评价
     */
    public TaskEvaluation selectTaskEvaluationByEvaluationId(Long evaluationId);

    /**
     * 查询任务评价列表
     * 
     * @param taskEvaluation 任务评价
     * @return 任务评价集合
     */
    public List<TaskEvaluation> selectTaskEvaluationList(TaskEvaluation taskEvaluation);

    /**
     * 新增任务评价
     * 
     * @param taskEvaluation 任务评价
     * @return 结果
     */
    public int insertTaskEvaluation(TaskEvaluation taskEvaluation);

    /**
     * 修改任务评价
     * 
     * @param taskEvaluation 任务评价
     * @return 结果
     */
    public int updateTaskEvaluation(TaskEvaluation taskEvaluation);

    /**
     * 删除任务评价
     * 
     * @param evaluationId 任务评价主键
     * @return 结果
     */
    public int deleteTaskEvaluationByEvaluationId(Long evaluationId);

    /**
     * 批量删除任务评价
     * 
     * @param evaluationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskEvaluationByEvaluationIds(Long[] evaluationIds);

    /**
     * 根据任务ID查询评价
     * 
     * @param taskId 任务ID
     * @return 任务评价
     */
    public TaskEvaluation selectTaskEvaluationByTaskId(Long taskId);

    /**
     * 根据接单人ID查询评价列表
     * 
     * @param receiverId 接单人ID
     * @return 任务评价集合
     */
    public List<TaskEvaluation> selectTaskEvaluationByReceiverId(Long receiverId);

    /**
     * 根据发单人ID查询评价列表
     * 
     * @param publisherId 发单人ID
     * @return 任务评价集合
     */
    public List<TaskEvaluation> selectTaskEvaluationByPublisherId(Long publisherId);

    /**
     * 统计接单人的平均评分
     * 
     * @param receiverId 接单人ID
     * @return 平均评分
     */
    public Double selectAverageRatingByReceiverId(Long receiverId);

    /**
     * 统计接单人的评价总数
     * 
     * @param receiverId 接单人ID
     * @return 评价总数
     */
    public Integer selectEvaluationCountByReceiverId(Long receiverId);

    /**
     * 统计各评分等级的数量
     *
     * @param receiverId 接单人ID
     * @return 评分统计
     */
    public List<Map<String, Object>> selectRatingStatsByReceiverId(Long receiverId);
}
