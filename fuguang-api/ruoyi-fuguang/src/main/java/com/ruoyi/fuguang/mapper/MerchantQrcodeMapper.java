package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.MerchantQrcode;

/**
 * 商家二维码信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface MerchantQrcodeMapper 
{
    /**
     * 查询商家二维码信息
     * 
     * @param qrcodeId 商家二维码信息主键
     * @return 商家二维码信息
     */
    public MerchantQrcode selectMerchantQrcodeByQrcodeId(String qrcodeId);

    /**
     * 根据商家ID查询商家二维码信息
     * 
     * @param merchantId 商家ID
     * @return 商家二维码信息
     */
    public MerchantQrcode selectMerchantQrcodeByMerchantId(Long merchantId);

    /**
     * 查询商家二维码信息列表
     * 
     * @param merchantQrcode 商家二维码信息
     * @return 商家二维码信息集合
     */
    public List<MerchantQrcode> selectMerchantQrcodeList(MerchantQrcode merchantQrcode);

    /**
     * 新增商家二维码信息
     * 
     * @param merchantQrcode 商家二维码信息
     * @return 结果
     */
    public int insertMerchantQrcode(MerchantQrcode merchantQrcode);

    /**
     * 修改商家二维码信息
     * 
     * @param merchantQrcode 商家二维码信息
     * @return 结果
     */
    public int updateMerchantQrcode(MerchantQrcode merchantQrcode);

    /**
     * 删除商家二维码信息
     * 
     * @param qrcodeId 商家二维码信息主键
     * @return 结果
     */
    public int deleteMerchantQrcodeByQrcodeId(Long qrcodeId);

    /**
     * 批量删除商家二维码信息
     * 
     * @param qrcodeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMerchantQrcodeByQrcodeIds(Long[] qrcodeIds);
}
