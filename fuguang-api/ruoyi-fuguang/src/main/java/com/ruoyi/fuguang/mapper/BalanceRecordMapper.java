package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.BalanceRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 余额变动记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface BalanceRecordMapper 
{
    /**
     * 查询余额变动记录
     * 
     * @param recordId 余额变动记录主键
     * @return 余额变动记录
     */
    public BalanceRecord selectBalanceRecordByRecordId(Long recordId);

    /**
     * 查询余额变动记录列表
     * 
     * @param balanceRecord 余额变动记录
     * @return 余额变动记录集合
     */
    public List<BalanceRecord> selectBalanceRecordList(BalanceRecord balanceRecord);

    /**
     * 根据用户ID查询余额变动记录列表
     * 
     * @param userId 用户ID
     * @return 余额变动记录集合
     */
    public List<BalanceRecord> selectBalanceRecordListByUserId(Long userId);

    /**
     * 根据用户ID和时间范围查询余额变动记录列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 余额变动记录集合
     */
    public List<BalanceRecord> selectBalanceRecordListByUserIdAndTime(@Param("userId") Long userId, 
                                                                     @Param("startTime") String startTime, 
                                                                     @Param("endTime") String endTime);

    /**
     * 新增余额变动记录
     * 
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    public int insertBalanceRecord(BalanceRecord balanceRecord);

    /**
     * 修改余额变动记录
     * 
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    public int updateBalanceRecord(BalanceRecord balanceRecord);


}
