package com.ruoyi.fuguang.mapper;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import com.ruoyi.fuguang.domain.TaskPayment;
import org.apache.ibatis.annotations.Param;

/**
 * 任务支付记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface TaskPaymentMapper 
{
    /**
     * 查询任务支付记录
     * 
     * @param paymentId 任务支付记录主键
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByPaymentId(Long paymentId);

    /**
     * 查询任务支付记录列表
     * 
     * @param taskPayment 任务支付记录
     * @return 任务支付记录集合
     */
    public List<TaskPayment> selectTaskPaymentList(TaskPayment taskPayment);

    /**
     * 新增任务支付记录
     * 
     * @param taskPayment 任务支付记录
     * @return 结果
     */
    public int insertTaskPayment(TaskPayment taskPayment);

    /**
     * 修改任务支付记录
     * 
     * @param taskPayment 任务支付记录
     * @return 结果
     */
    public int updateTaskPayment(TaskPayment taskPayment);

    /**
     * 删除任务支付记录
     * 
     * @param paymentId 任务支付记录主键
     * @return 结果
     */
    public int deleteTaskPaymentByPaymentId(Long paymentId);

    /**
     * 批量删除任务支付记录
     * 
     * @param paymentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskPaymentByPaymentIds(Long[] paymentIds);

    /**
     * 根据任务ID查询支付记录
     * 
     * @param taskId 任务ID
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByTaskId(Long taskId);

    /**
     * 根据订单号查询支付记录
     * 
     * @param orderNo 订单号
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByOrderNo(String orderNo);

    /**
     * 更新支付状态
     * 
     * @param orderNo 订单号
     * @param payStatus 支付状态
     * @param tradeNo 第三方交易号
     * @return 结果
     */
    public int updatePaymentStatusByOrderNo(@Param("orderNo") String orderNo, 
                                          @Param("payStatus") String payStatus, 
                                          @Param("tradeNo") String tradeNo);

    /**
     * 根据用户ID查询支付记录列表
     *
     * @param userId 用户ID
     * @return 任务支付记录集合
     */
    public List<TaskPayment> selectTaskPaymentListByUserId(Long userId);

    // ==================== 统计查询方法 ====================

    /**
     * 获取支付总览统计
     *
     * @return 统计数据
     */
    public Map<String, Object> getPaymentOverviewStatistics();

    /**
     * 获取今日支付统计
     *
     * @return 今日统计数据
     */
    public Map<String, Object> getTodayPaymentStatistics();

    /**
     * 获取本月支付统计
     *
     * @return 本月统计数据
     */
    public Map<String, Object> getMonthPaymentStatistics();

    /**
     * 获取支付趋势统计
     *
     * @param days 天数
     * @return 趋势统计数据
     */
    public List<Map<String, Object>> getPaymentTrendStatistics(@Param("days") int days);

    /**
     * 获取支付渠道分布统计
     *
     * @return 渠道分布数据
     */
    public List<Map<String, Object>> getPaymentChannelDistribution();

    /**
     * 获取支付异常统计
     *
     * @return 异常统计数据
     */
    public Map<String, Object> getPaymentExceptionStatistics();

    /**
     * 获取支付成功率统计
     *
     * @param days 天数
     * @return 成功率统计数据
     */
    public List<Map<String, Object>> getPaymentSuccessRateStatistics(@Param("days") int days);

    /**
     * 获取支付金额分布统计
     *
     * @return 金额分布数据
     */
    public List<Map<String, Object>> getPaymentAmountDistribution();

    /**
     * 获取用户支付行为分析
     *
     * @return 用户行为分析数据
     */
    public Map<String, Object> getUserPaymentBehaviorAnalysis();

    /**
     * 获取支付时段分析
     *
     * @return 时段分析数据
     */
    public List<Map<String, Object>> getPaymentTimeAnalysis();

    /**
     * 获取退款统计
     *
     * @return 退款统计数据
     */
    public Map<String, Object> getRefundStatistics();
}
