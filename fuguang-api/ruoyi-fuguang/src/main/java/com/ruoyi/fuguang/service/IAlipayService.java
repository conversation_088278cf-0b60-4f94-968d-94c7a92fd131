package com.ruoyi.fuguang.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 支付宝通用服务接口
 * 
 * <AUTHOR>
 */
public interface IAlipayService 
{
    /**
     * 创建支付宝APP支付订单
     * 
     * @param outTradeNo 商户订单号
     * @param totalAmount 支付金额
     * @param subject 订单标题
     * @param body 订单描述
     * @return 支付参数
     */
    Map<String, Object> createAppPayOrder(String outTradeNo, BigDecimal totalAmount, String subject, String body);

    /**
     * 创建支付宝H5支付订单
     * 
     * @param outTradeNo 商户订单号
     * @param totalAmount 支付金额
     * @param subject 订单标题
     * @param body 订单描述
     * @param notifyUrl 异步通知地址
     * @param returnUrl 同步跳转地址
     * @return 支付参数
     */
    Map<String, Object> createWapPayOrder(String outTradeNo, BigDecimal totalAmount, String subject, String body, String notifyUrl, String returnUrl);

    /**
     * 验证支付宝回调签名
     * 
     * @param params 回调参数
     * @return 验证结果
     */
    boolean verifyCallback(Map<String, String> params);

    /**
     * 查询支付订单状态
     * 
     * @param outTradeNo 商户订单号
     * @return 订单状态信息
     */
    Map<String, Object> queryPayOrder(String outTradeNo);

    /**
     * 关闭支付订单
     * 
     * @param outTradeNo 商户订单号
     * @return 关闭结果
     */
    boolean closePayOrder(String outTradeNo);

    /**
     * 申请退款
     * 
     * @param outTradeNo 商户订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    Map<String, Object> refund(String outTradeNo, BigDecimal refundAmount, String refundReason);

    /**
     * 查询退款状态
     * 
     * @param outTradeNo 商户订单号
     * @param outRequestNo 退款请求号
     * @return 退款状态信息
     */
    Map<String, Object> queryRefund(String outTradeNo, String outRequestNo);

    /**
     * 单笔转账到支付宝账户（提现）
     * 
     * @param outBizNo 商户转账唯一订单号
     * @param payeeAccount 收款方账户
     * @param amount 转账金额
     * @param payeeRealName 收款方真实姓名
     * @param remark 转账备注
     * @return 转账结果
     */
    Map<String, Object> transfer(String outBizNo, String payeeAccount, BigDecimal amount, String payeeRealName, String remark);

    /**
     * 查询转账订单状态
     * 
     * @param outBizNo 商户转账唯一订单号
     * @return 转账状态信息
     */
    Map<String, Object> queryTransfer(String outBizNo);
}
