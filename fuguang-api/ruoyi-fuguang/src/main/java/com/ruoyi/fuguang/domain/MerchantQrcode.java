package com.ruoyi.fuguang.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 商家二维码信息对象 merchant_qrcode
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class MerchantQrcode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 二维码ID */
    private String qrcodeId;

    /** 商家ID（商家申请ID） */
    @Excel(name = "商家ID")
    private Long merchantId;

    /** 商家名称 */
    @Excel(name = "商家名称")
    private String merchantName;

    /** 二维码内容 */
    @Excel(name = "二维码内容")
    private String qrcodeContent;

    /** 让利比例 */
    @Excel(name = "让利比例")
    private BigDecimal offerDiscounts;

    /** 二维码图片URL */
    @Excel(name = "二维码图片URL")
    private String qrcodeUrl;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;


}
