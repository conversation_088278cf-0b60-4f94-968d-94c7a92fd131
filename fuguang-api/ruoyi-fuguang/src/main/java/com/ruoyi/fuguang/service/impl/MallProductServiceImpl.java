package com.ruoyi.fuguang.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.MallProductMapper;
import com.ruoyi.fuguang.domain.MallProduct;
import com.ruoyi.fuguang.service.IMallProductService;

/**
 * 商品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MallProductServiceImpl implements IMallProductService
{
    @Autowired
    private MallProductMapper mallProductMapper;

    /**
     * 查询商品信息
     *
     * @param productId 商品信息主键
     * @return 商品信息
     */
    @Override
    public MallProduct selectMallProductByProductId(Long productId)
    {
        return mallProductMapper.selectMallProductByProductId(productId);
    }

    /**
     * 查询商品信息列表
     *
     * @param mallProduct 商品信息
     * @return 商品信息
     */
    @Override
    public List<MallProduct> selectMallProductList(MallProduct mallProduct)
    {
        return mallProductMapper.selectMallProductList(mallProduct);
    }

    /**
     * 新增商品信息
     *
     * @param mallProduct 商品信息
     * @return 结果
     */
    @Override
    public int insertMallProduct(MallProduct mallProduct)
    {
        mallProduct.setCreateTime(DateUtils.getNowDate());
        return mallProductMapper.insertMallProduct(mallProduct);
    }

    /**
     * 修改商品信息
     *
     * @param mallProduct 商品信息
     * @return 结果
     */
    @Override
    public int updateMallProduct(MallProduct mallProduct)
    {
        mallProduct.setUpdateTime(DateUtils.getNowDate());
        return mallProductMapper.updateMallProduct(mallProduct);
    }

    /**
     * 批量删除商品信息
     *
     * @param productIds 需要删除的商品信息主键
     * @return 结果
     */
    @Override
    public int deleteMallProductByProductIds(Long[] productIds)
    {
        return mallProductMapper.deleteMallProductByProductIds(productIds);
    }

    /**
     * 删除商品信息信息
     *
     * @param productId 商品信息主键
     * @return 结果
     */
    @Override
    public int deleteMallProductByProductId(Long productId)
    {
        return mallProductMapper.deleteMallProductByProductId(productId);
    }

    /**
     * 查询热门商品列表
     *
     * @param limit 限制数量
     * @return 商品列表
     */
    @Override
    public List<MallProduct> selectHotProductList(Integer limit)
    {
        return mallProductMapper.selectHotProductList(limit);
    }

    /**
     * 查询新品列表
     *
     * @param limit 限制数量
     * @return 商品列表
     */
    @Override
    public List<MallProduct> selectNewProductList(Integer limit)
    {
        return mallProductMapper.selectNewProductList(limit);
    }

    /**
     * 查询推荐商品列表
     *
     * @param limit 限制数量
     * @return 商品列表
     */
    @Override
    public List<MallProduct> selectRecommendProductList(Integer limit)
    {
        return mallProductMapper.selectRecommendProductList(limit);
    }

    /**
     * 根据分类查询商品列表
     *
     * @param categoryId 分类ID
     * @param limit 限制数量
     * @return 商品列表
     */
    @Override
    public List<MallProduct> selectProductListByCategoryId(Long categoryId, Integer limit)
    {
        return mallProductMapper.selectProductListByCategoryId(categoryId, limit);
    }

    /**
     * 搜索商品
     *
     * @param keyword 关键词
     * @return 商品列表
     */
    @Override
    public List<MallProduct> searchProducts(String keyword)
    {
        return mallProductMapper.searchProducts(keyword);
    }

    /**
     * 更新商品库存
     *
     * @param productId 商品ID
     * @param quantity 数量（正数增加，负数减少）
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateProductStock(Long productId, Integer quantity)
    {
        try {
            int result = mallProductMapper.updateProductStock(productId, quantity);
            return result > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 更新商品销量
     *
     * @param productId 商品ID
     * @param quantity 销量增加数量
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateProductSales(Long productId, Integer quantity)
    {
        try {
            int result = mallProductMapper.updateProductSales(productId, quantity);
            return result > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 校验商品名称是否唯一
     *
     * @param mallProduct 商品信息
     * @return 结果
     */
    @Override
    public String checkProductNameUnique(MallProduct mallProduct)
    {
        Long productId = StringUtils.isNull(mallProduct.getProductId()) ? -1L : mallProduct.getProductId();
        MallProduct info = mallProductMapper.checkProductNameUnique(mallProduct.getProductName());
        if (StringUtils.isNotNull(info) && info.getProductId().longValue() != productId.longValue())
        {
            return "1";
        }
        return "0";
    }

    /**
     * 上架商品
     *
     * @param productId 商品ID
     * @return 结果
     */
    @Override
    public int onShelfProduct(Long productId)
    {
        MallProduct product = new MallProduct();
        product.setProductId(productId);
        product.setProductStatus("0");
        product.setUpdateTime(DateUtils.getNowDate());
        return mallProductMapper.updateMallProduct(product);
    }

    /**
     * 下架商品
     *
     * @param productId 商品ID
     * @return 结果
     */
    @Override
    public int offShelfProduct(Long productId)
    {
        MallProduct product = new MallProduct();
        product.setProductId(productId);
        product.setProductStatus("1");
        product.setUpdateTime(DateUtils.getNowDate());
        return mallProductMapper.updateMallProduct(product);
    }

    /**
     * 批量上架商品
     *
     * @param productIds 商品ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int batchOnShelfProduct(Long[] productIds)
    {
        int count = 0;
        for (Long productId : productIds) {
            count += onShelfProduct(productId);
        }
        return count;
    }

    /**
     * 批量下架商品
     *
     * @param productIds 商品ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int batchOffShelfProduct(Long[] productIds)
    {
        int count = 0;
        for (Long productId : productIds) {
            count += offShelfProduct(productId);
        }
        return count;
    }
}