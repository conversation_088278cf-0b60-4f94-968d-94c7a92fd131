package com.ruoyi.fuguang.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 支付宝配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "alipay")
public class AlipayConfig
{
    /** 应用ID */
    private String appId;

    /** 商户私钥 */
    private String privateKey;

    /** 支付宝公钥 */
    private String publicKey;

    /** 服务器异步通知页面路径 */
    private String notifyUrl;

    /** 页面跳转同步通知页面路径 */
    private String returnUrl;

    /** 签名方式 */
    private String signType = "RSA2";

    /** 字符编码格式 */
    private String charset = "UTF-8";

    /** 支付宝网关 */
    private String gatewayUrl = "https://openapi.alipay.com/gateway.do";

    /** 日志记录目录 */
    private String logPath;

    /** 是否使用证书模式 */
    private boolean useCert = false;

    /** 应用公钥证书路径 */
    private String appCertPath;

    /** 支付宝公钥证书路径 */
    private String alipayCertPath;

    /** 支付宝根证书路径 */
    private String alipayRootCertPath;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getGatewayUrl() {
        return gatewayUrl;
    }

    public void setGatewayUrl(String gatewayUrl) {
        this.gatewayUrl = gatewayUrl;
    }

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public boolean isUseCert() {
        return useCert;
    }

    public void setUseCert(boolean useCert) {
        this.useCert = useCert;
    }

    public String getAppCertPath() {
        return appCertPath;
    }

    public void setAppCertPath(String appCertPath) {
        this.appCertPath = appCertPath;
    }

    public String getAlipayCertPath() {
        return alipayCertPath;
    }

    public void setAlipayCertPath(String alipayCertPath) {
        this.alipayCertPath = alipayCertPath;
    }

    public String getAlipayRootCertPath() {
        return alipayRootCertPath;
    }

    public void setAlipayRootCertPath(String alipayRootCertPath) {
        this.alipayRootCertPath = alipayRootCertPath;
    }
}
