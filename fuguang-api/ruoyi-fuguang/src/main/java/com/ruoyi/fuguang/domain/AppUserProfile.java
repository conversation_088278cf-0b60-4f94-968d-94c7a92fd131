package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户个人简介对象 app_user_profile
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AppUserProfile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 简介ID */
    private Long profileId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 信用分（默认100分） */
    @Excel(name = "信用分")
    private Integer creditScore;

    /** 任务分（完成任务获得） */
    @Excel(name = "任务分")
    private Integer taskScore;

    /** 扶贫救援标志（0无 1有） */
    @Excel(name = "扶贫救援标志", readConverterExp = "0=无,1=有")
    private String povertyReliefBadge;

    /** 个人描述 */
    @Excel(name = "个人描述")
    private String profileDesc;

    /** 个人介绍图片（JSON格式存储多张图片路径） */
    private String profileImages;

    /** 个人介绍短视频路径 */
    @Excel(name = "个人介绍短视频路径")
    private String profileVideo;

    /** 总任务数 */
    @Excel(name = "总任务数")
    private Integer totalTasks;

    /** 完成任务数 */
    @Excel(name = "完成任务数")
    private Integer completedTasks;

    /** 任务成功率 */
    @Excel(name = "任务成功率")
    private BigDecimal successRate;

    /** 总收益 */
    @Excel(name = "总收益")
    private BigDecimal totalEarnings;

    /** 用户等级 */
    @Excel(name = "用户等级")
    private Integer level;

    /** 经验值 */
    @Excel(name = "经验值")
    private Integer experience;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;


}
