package com.ruoyi.fuguang.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.fuguang.domain.PaymentCallbackLog;

/**
 * 支付回调日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IPaymentCallbackLogService 
{
    /**
     * 查询支付回调日志
     * 
     * @param callbackId 支付回调日志主键
     * @return 支付回调日志
     */
    public PaymentCallbackLog selectPaymentCallbackLogByCallbackId(Long callbackId);

    /**
     * 查询支付回调日志列表
     * 
     * @param paymentCallbackLog 支付回调日志
     * @return 支付回调日志集合
     */
    public List<PaymentCallbackLog> selectPaymentCallbackLogList(PaymentCallbackLog paymentCallbackLog);

    /**
     * 新增支付回调日志
     * 
     * @param paymentCallbackLog 支付回调日志
     * @return 结果
     */
    public int insertPaymentCallbackLog(PaymentCallbackLog paymentCallbackLog);

    /**
     * 修改支付回调日志
     * 
     * @param paymentCallbackLog 支付回调日志
     * @return 结果
     */
    public int updatePaymentCallbackLog(PaymentCallbackLog paymentCallbackLog);

    /**
     * 批量删除支付回调日志
     * 
     * @param callbackIds 需要删除的支付回调日志主键集合
     * @return 结果
     */
    public int deletePaymentCallbackLogByCallbackIds(Long[] callbackIds);

    /**
     * 删除支付回调日志信息
     * 
     * @param callbackId 支付回调日志主键
     * @return 结果
     */
    public int deletePaymentCallbackLogByCallbackId(Long callbackId);

    /**
     * 根据订单号查询回调日志
     * 
     * @param orderNo 订单号
     * @return 回调日志列表
     */
    public List<PaymentCallbackLog> selectPaymentCallbackLogByOrderNo(String orderNo);

    /**
     * 记录支付回调日志
     * 
     * @param orderNo 订单号
     * @param businessType 业务类型
     * @param payType 支付方式
     * @param payAmount 支付金额
     * @param callbackParams 回调参数
     * @param success 是否成功
     * @param errorMsg 错误信息
     * @return 结果
     */
    public int logPaymentCallback(String orderNo, String businessType, String payType, 
                                 java.math.BigDecimal payAmount, String callbackParams, 
                                 boolean success, String errorMsg);

    /**
     * 获取回调统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getCallbackStatistics();

    /**
     * 获取今日回调统计
     * 
     * @return 今日统计信息
     */
    public Map<String, Object> getTodayCallbackStatistics();

    /**
     * 获取回调成功率统计（按业务类型）
     * 
     * @return 成功率统计
     */
    public List<Map<String, Object>> getCallbackSuccessRateByBusinessType();

    /**
     * 获取回调趋势统计（最近7天）
     * 
     * @return 趋势统计
     */
    public List<Map<String, Object>> getCallbackTrendStatistics();

    /**
     * 重试回调
     * 
     * @param callbackId 回调日志ID
     * @return 结果
     */
    public boolean retryCallback(Long callbackId);

    /**
     * 批量重试回调
     * 
     * @param callbackIds 回调日志ID数组
     * @return 结果
     */
    public boolean batchRetryCallback(Long[] callbackIds);
}
