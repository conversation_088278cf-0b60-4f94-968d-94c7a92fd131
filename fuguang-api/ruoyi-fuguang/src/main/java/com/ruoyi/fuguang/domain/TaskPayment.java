package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 任务支付记录对象 task_payment
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TaskPayment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 支付记录ID */
    private Long paymentId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 任务标题 */
    @Excel(name = "任务标题")
    private String taskTitle;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String userName;

    /** 支付订单号 */
    @Excel(name = "支付订单号")
    private String orderNo;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmount;

    /** 支付方式（1支付宝 2微信 3余额） */
    @Excel(name = "支付方式", readConverterExp = "1=支付宝,2=微信,3=余额")
    private String payType;

    /** 支付状态（0待支付 1支付成功 2支付失败 3已退款） */
    @Excel(name = "支付状态", readConverterExp = "0=待支付,1=支付成功,2=支付失败,3=已退款")
    private String payStatus;

    /** 第三方交易号 */
    @Excel(name = "第三方交易号")
    private String tradeNo;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 通知时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "通知时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date notifyTime;

    /** 通知数据 */
    @Excel(name = "通知数据")
    private String notifyBody;

    /** 支付字符串 */
    @Excel(name = "支付字符串")
    private String payOrderString;
}
