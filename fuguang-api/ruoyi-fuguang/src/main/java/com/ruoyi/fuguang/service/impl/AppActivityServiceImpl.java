package com.ruoyi.fuguang.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppActivityMapper;
import com.ruoyi.fuguang.domain.AppActivity;
import com.ruoyi.fuguang.service.IAppActivityService;

/**
 * APP活动管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class AppActivityServiceImpl implements IAppActivityService 
{
    @Autowired
    private AppActivityMapper appActivityMapper;

    /**
     * 查询APP活动管理
     * 
     * @param activityId APP活动管理主键
     * @return APP活动管理
     */
    @Override
    public AppActivity selectAppActivityByActivityId(Long activityId)
    {
        return appActivityMapper.selectAppActivityByActivityId(activityId);
    }

    /**
     * 查询APP活动管理列表
     * 
     * @param appActivity APP活动管理
     * @return APP活动管理
     */
    @Override
    public List<AppActivity> selectAppActivityList(AppActivity appActivity)
    {
        return appActivityMapper.selectAppActivityList(appActivity);
    }



    /**
     * 查询当前有效的活动列表（在活动时间范围内且状态正常）
     *
     * @return APP活动集合
     */
    @Override
    public List<AppActivity> selectValidAppActivityList()
    {
        return appActivityMapper.selectValidAppActivityList();
    }

    /**
     * 新增APP活动管理
     * 
     * @param appActivity APP活动管理
     * @return 结果
     */
    @Override
    public int insertAppActivity(AppActivity appActivity)
    {
        appActivity.setCreateTime(DateUtils.getNowDate());
        return appActivityMapper.insertAppActivity(appActivity);
    }

    /**
     * 修改APP活动管理
     * 
     * @param appActivity APP活动管理
     * @return 结果
     */
    @Override
    public int updateAppActivity(AppActivity appActivity)
    {
        appActivity.setUpdateTime(DateUtils.getNowDate());
        return appActivityMapper.updateAppActivity(appActivity);
    }

    /**
     * 批量删除APP活动管理
     * 
     * @param activityIds 需要删除的APP活动管理主键
     * @return 结果
     */
    @Override
    public int deleteAppActivityByActivityIds(Long[] activityIds)
    {
        return appActivityMapper.deleteAppActivityByActivityIds(activityIds);
    }

    /**
     * 删除APP活动管理信息
     * 
     * @param activityId APP活动管理主键
     * @return 结果
     */
    @Override
    public int deleteAppActivityByActivityId(Long activityId)
    {
        return appActivityMapper.deleteAppActivityByActivityId(activityId);
    }
}
