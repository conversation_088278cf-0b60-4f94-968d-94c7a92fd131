package com.ruoyi.fuguang.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.TaskEvaluationMapper;
import com.ruoyi.fuguang.domain.TaskEvaluation;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.ITaskEvaluationService;
import com.ruoyi.fuguang.service.IAppTaskService;
import com.ruoyi.fuguang.service.IAppUserService;
import com.ruoyi.fuguang.service.IAppUserTimelineService;
import com.ruoyi.fuguang.domain.AppUser;

/**
 * 任务评价Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Service
public class TaskEvaluationServiceImpl implements ITaskEvaluationService 
{
    private static final Logger log = LoggerFactory.getLogger(TaskEvaluationServiceImpl.class);

    @Autowired
    private TaskEvaluationMapper taskEvaluationMapper;

    @Autowired
    private IAppTaskService appTaskService;

    @Autowired
    private IAppUserService appUserService;

    @Autowired
    private IAppUserTimelineService appUserTimelineService;

    /**
     * 查询任务评价
     * 
     * @param evaluationId 任务评价主键
     * @return 任务评价
     */
    @Override
    public TaskEvaluation selectTaskEvaluationByEvaluationId(Long evaluationId)
    {
        return taskEvaluationMapper.selectTaskEvaluationByEvaluationId(evaluationId);
    }

    /**
     * 查询任务评价列表
     * 
     * @param taskEvaluation 任务评价
     * @return 任务评价
     */
    @Override
    public List<TaskEvaluation> selectTaskEvaluationList(TaskEvaluation taskEvaluation)
    {
        return taskEvaluationMapper.selectTaskEvaluationList(taskEvaluation);
    }

    /**
     * 新增任务评价
     * 
     * @param taskEvaluation 任务评价
     * @return 结果
     */
    @Override
    public int insertTaskEvaluation(TaskEvaluation taskEvaluation)
    {
        taskEvaluation.setCreateTime(DateUtils.getNowDate());
        return taskEvaluationMapper.insertTaskEvaluation(taskEvaluation);
    }

    /**
     * 修改任务评价
     * 
     * @param taskEvaluation 任务评价
     * @return 结果
     */
    @Override
    public int updateTaskEvaluation(TaskEvaluation taskEvaluation)
    {
        taskEvaluation.setUpdateTime(DateUtils.getNowDate());
        return taskEvaluationMapper.updateTaskEvaluation(taskEvaluation);
    }

    /**
     * 批量删除任务评价
     * 
     * @param evaluationIds 需要删除的任务评价主键
     * @return 结果
     */
    @Override
    public int deleteTaskEvaluationByEvaluationIds(Long[] evaluationIds)
    {
        return taskEvaluationMapper.deleteTaskEvaluationByEvaluationIds(evaluationIds);
    }

    /**
     * 删除任务评价信息
     * 
     * @param evaluationId 任务评价主键
     * @return 结果
     */
    @Override
    public int deleteTaskEvaluationByEvaluationId(Long evaluationId)
    {
        return taskEvaluationMapper.deleteTaskEvaluationByEvaluationId(evaluationId);
    }

    /**
     * 根据任务ID查询评价
     * 
     * @param taskId 任务ID
     * @return 任务评价
     */
    @Override
    public TaskEvaluation selectTaskEvaluationByTaskId(Long taskId)
    {
        return taskEvaluationMapper.selectTaskEvaluationByTaskId(taskId);
    }

    /**
     * 根据接单人ID查询评价列表
     * 
     * @param receiverId 接单人ID
     * @return 任务评价集合
     */
    @Override
    public List<TaskEvaluation> selectTaskEvaluationByReceiverId(Long receiverId)
    {
        return taskEvaluationMapper.selectTaskEvaluationByReceiverId(receiverId);
    }

    /**
     * 根据发单人ID查询评价列表
     * 
     * @param publisherId 发单人ID
     * @return 任务评价集合
     */
    @Override
    public List<TaskEvaluation> selectTaskEvaluationByPublisherId(Long publisherId)
    {
        return taskEvaluationMapper.selectTaskEvaluationByPublisherId(publisherId);
    }

    /**
     * 统计接单人的平均评分
     * 
     * @param receiverId 接单人ID
     * @return 平均评分
     */
    @Override
    public Double selectAverageRatingByReceiverId(Long receiverId)
    {
        Double avgRating = taskEvaluationMapper.selectAverageRatingByReceiverId(receiverId);
        return avgRating != null ? avgRating : 0.0;
    }

    /**
     * 统计接单人的评价总数
     * 
     * @param receiverId 接单人ID
     * @return 评价总数
     */
    @Override
    public Integer selectEvaluationCountByReceiverId(Long receiverId)
    {
        Integer count = taskEvaluationMapper.selectEvaluationCountByReceiverId(receiverId);
        return count != null ? count : 0;
    }

    /**
     * 统计各评分等级的数量
     * 
     * @param receiverId 接单人ID
     * @return 评分统计
     */
    @Override
    public List<Map<String, Object>> selectRatingStatsByReceiverId(Long receiverId)
    {
        return taskEvaluationMapper.selectRatingStatsByReceiverId(receiverId);
    }

    /**
     * 提交任务评价
     * 
     * @param taskId 任务ID
     * @param publisherId 发单人ID
     * @param rating 评分
     * @param evaluationContent 评价内容
     * @param evaluationTags 评价标签
     * @param isAnonymous 是否匿名
     * @return 结果
     */
    @Override
    @Transactional
    public boolean submitTaskEvaluation(Long taskId, Long publisherId, Integer rating, 
                                      String evaluationContent, String evaluationTags, String isAnonymous)
    {
        try {
            // 检查是否可以评价
            if (!canEvaluateTask(taskId, publisherId)) {
                log.error("任务不能评价，任务ID：{}，发单人ID：{}", taskId, publisherId);
                return false;
            }
            // 获取任务信息
            AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
            if (task == null) {
                log.error("任务不存在，任务ID：{}", taskId);
                return false;
            }
            // 获取发单人信息
            AppUser publisher = appUserService.selectAppUserByUserId(publisherId);
            if (publisher == null) {
                log.error("发单人不存在，发单人ID：{}", publisherId);
                return false;
            }
            // 获取接单人信息
            AppUser receiver = appUserService.selectAppUserByUserId(task.getReceiverId());
            if (receiver == null) {
                log.error("接单人不存在，接单人ID：{}", task.getReceiverId());
                return false;
            }
            // 创建评价记录
            TaskEvaluation evaluation = new TaskEvaluation();
            evaluation.setTaskId(taskId);
            evaluation.setTaskTitle(task.getTaskTitle());
            evaluation.setPublisherId(publisherId);
            evaluation.setPublisherName(publisher.getNickName());
            evaluation.setReceiverId(task.getReceiverId());
            evaluation.setReceiverName(receiver.getNickName());
            evaluation.setRating(rating);
            evaluation.setEvaluationContent(evaluationContent);
            evaluation.setEvaluationTags(evaluationTags);
            evaluation.setIsAnonymous(isAnonymous != null ? isAnonymous : "0");
            evaluation.setStatus("0");
            evaluation.setCreateBy(SecurityUtils.getUsername());
            int result = insertTaskEvaluation(evaluation);
            if (result > 0) {
                // 添加评价事件到接单人的时间线
                String eventTitle = "收到任务评价";
                String eventDesc = String.format("任务「%s」收到了%d星评价", task.getTaskTitle(), rating);
                if (evaluationContent != null && !evaluationContent.trim().isEmpty()) {
                    eventDesc += "：" + evaluationContent;
                }
                String eventData = String.format("{\"taskId\":%d,\"rating\":%d,\"evaluationId\":%d,\"isAnonymous\":\"%s\"}",
                                                taskId, rating, evaluation.getEvaluationId(), isAnonymous);
                appUserTimelineService.addCustomEvent(
                    task.getReceiverId(),
                    "task_evaluation",
                    eventTitle,
                    eventDesc,
                    eventData,
                    DateUtils.getNowDate(),
                    "star",
                    rating >= 4 ? "success" : (rating >= 3 ? "warning" : "danger")
                );
                log.info("任务评价提交成功，任务ID：{}，评价ID：{}", taskId, evaluation.getEvaluationId());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("提交任务评价失败，任务ID：{}，发单人ID：{}", taskId, publisherId, e);
            return false;
        }
    }

    /**
     * 检查任务是否可以评价
     * 
     * @param taskId 任务ID
     * @param publisherId 发单人ID
     * @return 是否可以评价
     */
    @Override
    public boolean canEvaluateTask(Long taskId, Long publisherId)
    {
        // 获取任务信息
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return false;
        }

        // 检查是否是发单人
        if (!task.getPublisherId().equals(publisherId)) {
            return false;
        }

        // 检查任务状态是否为已完成
        if (!"2".equals(task.getTaskStatus())) {
            return false;
        }

        // 检查是否已经评价过
        TaskEvaluation existingEvaluation = selectTaskEvaluationByTaskId(taskId);
        if (existingEvaluation != null) {
            return false;
        }

        return true;
    }

    /**
     * 获取用户评价统计信息
     * 
     * @param receiverId 接单人ID
     * @return 评价统计信息
     */
    @Override
    public Map<String, Object> getEvaluationStatistics(Long receiverId)
    {
        Map<String, Object> statistics = new HashMap<>();
        
        // 平均评分
        Double avgRating = selectAverageRatingByReceiverId(receiverId);
        statistics.put("averageRating", avgRating);
        
        // 评价总数
        Integer totalCount = selectEvaluationCountByReceiverId(receiverId);
        statistics.put("totalCount", totalCount);
        
        // 各评分等级统计
        List<Map<String, Object>> ratingStats = selectRatingStatsByReceiverId(receiverId);
        statistics.put("ratingStats", ratingStats);
        
        return statistics;
    }
}
