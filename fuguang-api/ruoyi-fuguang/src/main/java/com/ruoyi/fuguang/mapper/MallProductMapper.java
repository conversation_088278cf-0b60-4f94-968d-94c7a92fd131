package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.MallProduct;
import org.apache.ibatis.annotations.Param;

/**
 * 商品信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface MallProductMapper 
{
    /**
     * 查询商品信息
     * 
     * @param productId 商品信息主键
     * @return 商品信息
     */
    public MallProduct selectMallProductByProductId(Long productId);

    /**
     * 查询商品信息列表
     * 
     * @param mallProduct 商品信息
     * @return 商品信息集合
     */
    public List<MallProduct> selectMallProductList(MallProduct mallProduct);

    /**
     * 新增商品信息
     * 
     * @param mallProduct 商品信息
     * @return 结果
     */
    public int insertMallProduct(MallProduct mallProduct);

    /**
     * 修改商品信息
     * 
     * @param mallProduct 商品信息
     * @return 结果
     */
    public int updateMallProduct(MallProduct mallProduct);

    /**
     * 删除商品信息
     * 
     * @param productId 商品信息主键
     * @return 结果
     */
    public int deleteMallProductByProductId(Long productId);

    /**
     * 批量删除商品信息
     * 
     * @param productIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMallProductByProductIds(Long[] productIds);

    /**
     * 查询热门商品列表
     * 
     * @param limit 限制数量
     * @return 商品列表
     */
    public List<MallProduct> selectHotProductList(@Param("limit") Integer limit);

    /**
     * 查询新品列表
     * 
     * @param limit 限制数量
     * @return 商品列表
     */
    public List<MallProduct> selectNewProductList(@Param("limit") Integer limit);

    /**
     * 查询推荐商品列表
     * 
     * @param limit 限制数量
     * @return 商品列表
     */
    public List<MallProduct> selectRecommendProductList(@Param("limit") Integer limit);

    /**
     * 根据分类查询商品列表
     * 
     * @param categoryId 分类ID
     * @param limit 限制数量
     * @return 商品列表
     */
    public List<MallProduct> selectProductListByCategoryId(@Param("categoryId") Long categoryId, @Param("limit") Integer limit);

    /**
     * 搜索商品
     * 
     * @param keyword 关键词
     * @return 商品列表
     */
    public List<MallProduct> searchProducts(@Param("keyword") String keyword);

    /**
     * 更新商品库存
     * 
     * @param productId 商品ID
     * @param quantity 数量（正数增加，负数减少）
     * @return 结果
     */
    public int updateProductStock(@Param("productId") Long productId, @Param("quantity") Integer quantity);

    /**
     * 更新商品销量
     *
     * @param productId 商品ID
     * @param quantity 销量增加数量
     * @return 结果
     */
    public int updateProductSales(@Param("productId") Long productId, @Param("quantity") Integer quantity);

    /**
     * 校验商品名称是否唯一
     *
     * @param productName 商品名称
     * @return 商品信息
     */
    public MallProduct checkProductNameUnique(@Param("productName") String productName);
}
