package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.BalanceRecordMapper;
import com.ruoyi.fuguang.mapper.UserBalanceMapper;
import com.ruoyi.fuguang.mapper.CommissionBillMapper;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.domain.BalanceRecord;
import com.ruoyi.fuguang.domain.UserBalance;
import com.ruoyi.fuguang.service.IBalanceRecordService;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.fuguang.service.IAppUserService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 余额变动记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class BalanceRecordServiceImpl implements IBalanceRecordService
{
    @Autowired
    private BalanceRecordMapper balanceRecordMapper;

    @Autowired
    private UserBalanceMapper userBalanceMapper;

    @Autowired
    private CommissionBillMapper commissionBillMapper;

    @Autowired
    private ICommissionBillService commissionBillService;

    @Autowired
    private IAppUserService appUserService;

    /**
     * 查询余额变动记录
     * 
     * @param recordId 余额变动记录主键
     * @return 余额变动记录
     */
    @Override
    public BalanceRecord selectBalanceRecordByRecordId(Long recordId)
    {
        return balanceRecordMapper.selectBalanceRecordByRecordId(recordId);
    }

    /**
     * 查询余额变动记录列表
     * 
     * @param balanceRecord 余额变动记录
     * @return 余额变动记录
     */
    @Override
    public List<BalanceRecord> selectBalanceRecordList(BalanceRecord balanceRecord)
    {
        return balanceRecordMapper.selectBalanceRecordList(balanceRecord);
    }



    /**
     * 根据用户ID和时间范围查询余额变动记录列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 余额变动记录集合
     */
    @Override
    public List<BalanceRecord> selectBalanceRecordListByUserIdAndTime(Long userId, String startTime, String endTime)
    {
        return balanceRecordMapper.selectBalanceRecordListByUserIdAndTime(userId, startTime, endTime);
    }

    /**
     * 新增余额变动记录
     * 
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    @Override
    public int insertBalanceRecord(BalanceRecord balanceRecord)
    {
        balanceRecord.setCreateTime(DateUtils.getNowDate());
        return balanceRecordMapper.insertBalanceRecord(balanceRecord);
    }

    /**
     * 修改余额变动记录
     *
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    @Override
    public int updateBalanceRecord(BalanceRecord balanceRecord)
    {
        return balanceRecordMapper.updateBalanceRecord(balanceRecord);
    }



    /**
     * 增加用户收入
     * 统一的收入增加入口，会同时更新用户余额、记录变动、更新佣金账单
     *
     * @param userId 用户ID
     * @param amount 收入金额
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param businessNo 业务单号
     * @param description 变动说明
     * @param incomeType 收入类型（1任务佣金 2推荐奖励 3其他收入）
     * @return 结果
     */
    @Override
    @Transactional
    public boolean addIncome(Long userId, BigDecimal amount, String businessType,
                           Long businessId, String businessNo, String description, String incomeType)
    {
        try {
            // 1. 获取或初始化用户余额
            UserBalance userBalance = userBalanceMapper.selectUserBalanceByUserId(userId);
            if (userBalance == null) {
                // 初始化用户余额
                AppUser appUser = appUserService.selectAppUserByUserId(userId);
                userBalance = new UserBalance();
                userBalance.setUserId(userId);
                userBalance.setUserName(appUser != null ? appUser.getNickName() : "");
                userBalance.setTotalBalance(BigDecimal.ZERO);
                userBalance.setAvailableBalance(BigDecimal.ZERO);
                userBalance.setFrozenBalance(BigDecimal.ZERO);
                userBalance.setTotalIncome(BigDecimal.ZERO);
                userBalance.setTotalExpense(BigDecimal.ZERO);
                userBalance.setTotalWithdraw(BigDecimal.ZERO);
                userBalance.setCreateTime(DateUtils.getNowDate());
                userBalanceMapper.insertUserBalance(userBalance);
            }

            BigDecimal balanceBefore = userBalance.getAvailableBalance();

            // 2. 更新用户余额
            int result = userBalanceMapper.increaseBalance(userId, amount);
            if (result <= 0) {
                return false;
            }

            // 3. 更新累计收入
            userBalanceMapper.updateTotalIncome(userId, amount);

            // 4. 记录余额变动
            BalanceRecord record = new BalanceRecord();
            record.setUserId(userId);
            record.setUserName(userBalance.getUserName());
            record.setChangeType("1"); // 收入
            record.setIncomeType(incomeType);
            record.setChangeAmount(amount);
            record.setBalanceBefore(balanceBefore);
            record.setBalanceAfter(balanceBefore.add(amount));
            record.setBusinessType(businessType);
            record.setBusinessId(businessId);
            record.setBusinessNo(businessNo);
            record.setDescription(description);
            insertBalanceRecord(record);

            // 5. 根据收入类型更新佣金账单
            updateCommissionBillByIncomeType(userId, userBalance.getUserName(), amount, incomeType);

            return true;
        } catch (Exception e) {
            throw new RuntimeException("增加用户收入失败：" + e.getMessage(), e);
        }
    }

    /**
     * 增加用户支出
     * 统一的支出增加入口，会同时更新用户余额、记录变动、更新佣金账单
     *
     * @param userId 用户ID
     * @param amount 支出金额
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param businessNo 业务单号
     * @param description 变动说明
     * @return 结果
     */
    @Override
    @Transactional
    public boolean addExpense(Long userId, BigDecimal amount, String businessType,
                            Long businessId, String businessNo, String description)
    {
        try {
            // 1. 获取用户余额
            UserBalance userBalance = userBalanceMapper.selectUserBalanceByUserId(userId);
            if (userBalance == null || userBalance.getAvailableBalance().compareTo(amount) < 0) {
                return false;
            }

            BigDecimal balanceBefore = userBalance.getAvailableBalance();

            // 2. 更新用户余额
            int result = userBalanceMapper.decreaseBalance(userId, amount);
            if (result <= 0) {
                return false;
            }

            // 3. 更新累计支出
            userBalanceMapper.updateTotalExpense(userId, amount);

            // 4. 记录余额变动
            BalanceRecord record = new BalanceRecord();
            record.setUserId(userId);
            record.setUserName(userBalance.getUserName());
            record.setChangeType("2"); // 支出
            record.setChangeAmount(amount);
            record.setBalanceBefore(balanceBefore);
            record.setBalanceAfter(balanceBefore.subtract(amount));
            record.setBusinessType(businessType);
            record.setBusinessId(businessId);
            record.setBusinessNo(businessNo);
            record.setDescription(description);
            insertBalanceRecord(record);

            // 5. 更新佣金账单的总支出
            commissionBillService.updateTotalExpense(userId, userBalance.getUserName(), amount);

            // 6. 如果是提现，更新佣金账单的提现金额
            if ("withdraw".equals(businessType)) {
                commissionBillService.updateWithdrawAmount(userId, userBalance.getUserName(), amount);
            }

            return true;
        } catch (Exception e) {
            throw new RuntimeException("增加用户支出失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据收入类型更新佣金账单
     *
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 金额
     * @param incomeType 收入类型
     */
    private void updateCommissionBillByIncomeType(Long userId, String userName, BigDecimal amount, String incomeType) {
        if ("1".equals(incomeType)) {
            // 任务佣金
            commissionBillService.updateTaskCommission(userId, userName, amount);
        } else if ("2".equals(incomeType)) {
            // 推荐奖励
            commissionBillService.updateRecommendReward(userId, userName, amount);
        } else if ("3".equals(incomeType)) {
            // 其他收入
            commissionBillService.updateOtherIncome(userId, userName, amount);
        }
    }

    /**
     * 冻结用户余额
     * 统一的余额冻结入口，会同时更新用户余额、记录变动
     *
     * @param userId 用户ID
     * @param amount 冻结金额
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param businessNo 业务单号
     * @param description 变动说明
     * @return 结果
     */
    @Override
    @Transactional
    public boolean freezeBalance(Long userId, BigDecimal amount, String businessType,
                               Long businessId, String businessNo, String description)
    {
        try {
            // 1. 获取用户余额
            UserBalance userBalance = userBalanceMapper.selectUserBalanceByUserId(userId);
            if (userBalance == null || userBalance.getAvailableBalance().compareTo(amount) < 0) {
                return false;
            }

            BigDecimal balanceBefore = userBalance.getAvailableBalance();

            // 2. 更新用户余额（可用余额减少，冻结余额增加）
            int result = userBalanceMapper.freezeBalance(userId, amount);
            if (result <= 0) {
                return false;
            }

            // 3. 获取用户信息
            AppUser appUser = appUserService.selectAppUserByUserId(userId);
            String userName = appUser != null ? appUser.getNickName() : "";

            // 4. 记录余额变动
            BalanceRecord balanceRecord = new BalanceRecord();
            balanceRecord.setUserId(userId);
            balanceRecord.setUserName(userName);
            balanceRecord.setChangeType("3"); // 冻结
            balanceRecord.setChangeAmount(amount);
            balanceRecord.setBalanceBefore(balanceBefore);
            balanceRecord.setBalanceAfter(balanceBefore.subtract(amount));
            balanceRecord.setBusinessType(businessType);
            balanceRecord.setBusinessId(businessId);
            balanceRecord.setBusinessNo(businessNo);
            balanceRecord.setDescription(description);
            balanceRecord.setCreateTime(DateUtils.getNowDate());

            insertBalanceRecord(balanceRecord);

            return true;
        } catch (Exception e) {
            throw new RuntimeException("冻结用户余额失败：" + e.getMessage(), e);
        }
    }

    /**
     * 解冻用户余额
     * 统一的余额解冻入口，会同时更新用户余额、记录变动
     *
     * @param userId 用户ID
     * @param amount 解冻金额
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param businessNo 业务单号
     * @param description 变动说明
     * @return 结果
     */
    @Override
    @Transactional
    public boolean unfreezeBalance(Long userId, BigDecimal amount, String businessType,
                                 Long businessId, String businessNo, String description)
    {
        try {
            // 1. 获取用户余额
            UserBalance userBalance = userBalanceMapper.selectUserBalanceByUserId(userId);
            if (userBalance == null || userBalance.getFrozenBalance().compareTo(amount) < 0) {
                return false;
            }

            BigDecimal balanceBefore = userBalance.getAvailableBalance();

            // 2. 更新用户余额（冻结余额减少，可用余额增加）
            int result = userBalanceMapper.unfreezeBalance(userId, amount);
            if (result <= 0) {
                return false;
            }

            // 3. 获取用户信息
            AppUser appUser = appUserService.selectAppUserByUserId(userId);
            String userName = appUser != null ? appUser.getNickName() : "";

            // 4. 记录余额变动
            BalanceRecord balanceRecord = new BalanceRecord();
            balanceRecord.setUserId(userId);
            balanceRecord.setUserName(userName);
            balanceRecord.setChangeType("4"); // 解冻
            balanceRecord.setChangeAmount(amount);
            balanceRecord.setBalanceBefore(balanceBefore);
            balanceRecord.setBalanceAfter(balanceBefore.add(amount));
            balanceRecord.setBusinessType(businessType);
            balanceRecord.setBusinessId(businessId);
            balanceRecord.setBusinessNo(businessNo);
            balanceRecord.setDescription(description);
            balanceRecord.setCreateTime(DateUtils.getNowDate());

            insertBalanceRecord(balanceRecord);

            return true;
        } catch (Exception e) {
            throw new RuntimeException("解冻用户余额失败：" + e.getMessage(), e);
        }
    }
}
