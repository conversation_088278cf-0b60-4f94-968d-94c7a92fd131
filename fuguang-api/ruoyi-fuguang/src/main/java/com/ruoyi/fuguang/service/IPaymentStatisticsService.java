package com.ruoyi.fuguang.service;

import java.util.List;
import java.util.Map;

/**
 * 支付统计分析Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IPaymentStatisticsService 
{
    /**
     * 获取支付统计概览
     * 
     * @return 统计概览数据
     */
    public Map<String, Object> getPaymentOverview();

    /**
     * 获取支付趋势数据
     * 
     * @param period 时间周期（7d, 30d, 90d）
     * @return 趋势数据
     */
    public List<Map<String, Object>> getPaymentTrend(String period);

    /**
     * 获取支付渠道分布
     * 
     * @return 渠道分布数据
     */
    public List<Map<String, Object>> getPaymentChannelDistribution();

    /**
     * 获取各业务类型支付统计
     * 
     * @return 业务类型统计数据
     */
    public List<Map<String, Object>> getBusinessTypeStatistics();

    /**
     * 获取支付异常统计
     * 
     * @return 异常统计数据
     */
    public Map<String, Object> getPaymentExceptionStatistics();

    /**
     * 获取今日支付统计
     * 
     * @return 今日统计数据
     */
    public Map<String, Object> getTodayPaymentStatistics();

    /**
     * 获取本月支付统计
     * 
     * @return 本月统计数据
     */
    public Map<String, Object> getMonthPaymentStatistics();

    /**
     * 获取支付成功率统计
     * 
     * @param period 时间周期
     * @return 成功率统计
     */
    public List<Map<String, Object>> getPaymentSuccessRate(String period);

    /**
     * 获取支付金额分布统计
     * 
     * @return 金额分布统计
     */
    public List<Map<String, Object>> getPaymentAmountDistribution();

    /**
     * 获取用户支付行为分析
     * 
     * @return 用户行为分析数据
     */
    public Map<String, Object> getUserPaymentBehaviorAnalysis();

    /**
     * 获取支付时段分析
     * 
     * @return 时段分析数据
     */
    public List<Map<String, Object>> getPaymentTimeAnalysis();

    /**
     * 获取退款统计
     * 
     * @return 退款统计数据
     */
    public Map<String, Object> getRefundStatistics();

    /**
     * 获取支付平台对比统计
     * 
     * @return 平台对比数据
     */
    public List<Map<String, Object>> getPaymentPlatformComparison();
}
