package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.AppActivity;

/**
 * APP活动管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface AppActivityMapper 
{
    /**
     * 查询APP活动管理
     * 
     * @param activityId APP活动管理主键
     * @return APP活动管理
     */
    public AppActivity selectAppActivityByActivityId(Long activityId);

    /**
     * 查询APP活动管理列表
     * 
     * @param appActivity APP活动管理
     * @return APP活动管理集合
     */
    public List<AppActivity> selectAppActivityList(AppActivity appActivity);



    /**
     * 查询当前有效的活动列表（在活动时间范围内且状态正常）
     *
     * @return APP活动集合
     */
    public List<AppActivity> selectValidAppActivityList();

    /**
     * 新增APP活动管理
     * 
     * @param appActivity APP活动管理
     * @return 结果
     */
    public int insertAppActivity(AppActivity appActivity);

    /**
     * 修改APP活动管理
     * 
     * @param appActivity APP活动管理
     * @return 结果
     */
    public int updateAppActivity(AppActivity appActivity);

    /**
     * 删除APP活动管理
     * 
     * @param activityId APP活动管理主键
     * @return 结果
     */
    public int deleteAppActivityByActivityId(Long activityId);

    /**
     * 批量删除APP活动管理
     * 
     * @param activityIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppActivityByActivityIds(Long[] activityIds);
}
