package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.fuguang.config.AlipayConfig;
import com.ruoyi.fuguang.service.IAlipayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 支付宝通用服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class AlipayServiceImpl implements IAlipayService 
{
    private static final Logger log = LoggerFactory.getLogger(AlipayServiceImpl.class);

    @Autowired
    private AlipayConfig alipayConfig;

    /**
     * 获取支付宝客户端
     */
    private AlipayClient getAlipayClient() {
        try {
            if (alipayConfig.isUseCert()) {
                // 使用证书模式
                CertAlipayRequest certAlipayRequest = new CertAlipayRequest();
                certAlipayRequest.setServerUrl(alipayConfig.getGatewayUrl());
                certAlipayRequest.setAppId(alipayConfig.getAppId());
                certAlipayRequest.setPrivateKey(alipayConfig.getPrivateKey());
                certAlipayRequest.setFormat("json");
                certAlipayRequest.setCharset(alipayConfig.getCharset());
                certAlipayRequest.setSignType(alipayConfig.getSignType());
                certAlipayRequest.setCertPath(alipayConfig.getAppCertPath());
                certAlipayRequest.setAlipayPublicCertPath(alipayConfig.getAlipayCertPath());
                certAlipayRequest.setRootCertPath(alipayConfig.getAlipayRootCertPath());
                return new DefaultAlipayClient(certAlipayRequest);
            } else {
                // 使用公钥模式
                return new DefaultAlipayClient(
                    alipayConfig.getGatewayUrl(),
                    alipayConfig.getAppId(),
                    alipayConfig.getPrivateKey(),
                    "json",
                    alipayConfig.getCharset(),
                    alipayConfig.getPublicKey(),
                    alipayConfig.getSignType()
                );
            }
        } catch (AlipayApiException e) {
            log.error("创建支付宝客户端失败", e);
            throw new RuntimeException("创建支付宝客户端失败：" + e.getMessage());
        }
    }

    /**
     * 创建支付宝APP支付订单
     */
    @Override
    public Map<String, Object> createAppPayOrder(String outTradeNo, BigDecimal totalAmount, String subject, String body)
    {
        try {
            AlipayClient alipayClient = getAlipayClient();
            AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
            
            // 设置异步通知地址
            request.setNotifyUrl(alipayConfig.getNotifyUrl());
            
            // 设置请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", outTradeNo);
            bizContent.put("total_amount", totalAmount.toString());
            bizContent.put("subject", subject);
            bizContent.put("body", body);
            bizContent.put("product_code", "QUICK_MSECURITY_PAY");
            
            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            
            // 调用SDK生成表单
            AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
            
            Map<String, Object> result = new HashMap<>();
            if (response.isSuccess()) {
                result.put("success", true);
                result.put("orderString", response.getBody());
                result.put("outTradeNo", outTradeNo);
                log.info("支付宝APP支付订单创建成功，订单号：{}", outTradeNo);
            } else {
                result.put("success", false);
                result.put("errorMsg", response.getSubMsg());
                log.error("支付宝APP支付订单创建失败，订单号：{}，错误信息：{}", outTradeNo, response.getSubMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("创建支付宝APP支付订单异常，订单号：{}", outTradeNo, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMsg", "创建支付订单异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 创建支付宝H5支付订单
     */
    @Override
    public Map<String, Object> createWapPayOrder(String outTradeNo, BigDecimal totalAmount, String subject, String body, String notifyUrl, String returnUrl) 
    {
        try {
            AlipayClient alipayClient = getAlipayClient();
            AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
            
            // 设置异步通知地址
            request.setNotifyUrl(notifyUrl);
            // 设置同步跳转地址
            request.setReturnUrl(returnUrl);
            
            // 设置请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", outTradeNo);
            bizContent.put("total_amount", totalAmount.toString());
            bizContent.put("subject", subject);
            bizContent.put("body", body);
            bizContent.put("product_code", "QUICK_WAP_WAY");
            
            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            
            // 调用SDK生成表单
            AlipayTradeWapPayResponse response = alipayClient.pageExecute(request);
            
            Map<String, Object> result = new HashMap<>();
            if (response.isSuccess()) {
                result.put("success", true);
                result.put("form", response.getBody());
                result.put("outTradeNo", outTradeNo);
                log.info("支付宝H5支付订单创建成功，订单号：{}", outTradeNo);
            } else {
                result.put("success", false);
                result.put("errorMsg", response.getSubMsg());
                log.error("支付宝H5支付订单创建失败，订单号：{}，错误信息：{}", outTradeNo, response.getSubMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("创建支付宝H5支付订单异常，订单号：{}", outTradeNo, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMsg", "创建支付订单异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 验证支付宝回调签名
     */
    @Override
    public boolean verifyCallback(Map<String, String> params) 
    {
        try {
            return AlipaySignature.rsaCheckV1(
                params, 
                alipayConfig.getPublicKey(), 
                alipayConfig.getCharset(), 
                alipayConfig.getSignType()
            );
        } catch (AlipayApiException e) {
            log.error("支付宝回调签名验证异常", e);
            return false;
        }
    }

    /**
     * 查询支付订单状态
     */
    @Override
    public Map<String, Object> queryPayOrder(String outTradeNo) 
    {
        try {
            AlipayClient alipayClient = getAlipayClient();
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", outTradeNo);
            
            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            
            AlipayTradeQueryResponse response = alipayClient.execute(request);
            
            Map<String, Object> result = new HashMap<>();
            if (response.isSuccess()) {
                result.put("success", true);
                result.put("tradeStatus", response.getTradeStatus());
                result.put("tradeNo", response.getTradeNo());
                result.put("totalAmount", response.getTotalAmount());
                result.put("buyerPayAmount", response.getBuyerPayAmount());
                log.info("查询支付订单状态成功，订单号：{}，状态：{}", outTradeNo, response.getTradeStatus());
            } else {
                result.put("success", false);
                result.put("errorMsg", response.getSubMsg());
                log.error("查询支付订单状态失败，订单号：{}，错误信息：{}", outTradeNo, response.getSubMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("查询支付订单状态异常，订单号：{}", outTradeNo, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMsg", "查询订单状态异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 关闭支付订单
     */
    @Override
    public boolean closePayOrder(String outTradeNo)
    {
        try {
            AlipayClient alipayClient = getAlipayClient();
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();

            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", outTradeNo);

            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));

            AlipayTradeCloseResponse response = alipayClient.execute(request);

            if (response.isSuccess()) {
                log.info("关闭支付订单成功，订单号：{}", outTradeNo);
                return true;
            } else {
                log.error("关闭支付订单失败，订单号：{}，错误信息：{}", outTradeNo, response.getSubMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("关闭支付订单异常，订单号：{}", outTradeNo, e);
            return false;
        }
    }

    /**
     * 申请退款
     */
    @Override
    public Map<String, Object> refund(String outTradeNo, BigDecimal refundAmount, String refundReason)
    {
        try {
            AlipayClient alipayClient = getAlipayClient();
            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();

            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", outTradeNo);
            bizContent.put("refund_amount", refundAmount.toString());
            bizContent.put("refund_reason", refundReason);
            bizContent.put("out_request_no", outTradeNo + "_refund_" + System.currentTimeMillis());

            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));

            AlipayTradeRefundResponse response = alipayClient.execute(request);

            Map<String, Object> result = new HashMap<>();
            if (response.isSuccess()) {
                result.put("success", true);
                result.put("tradeNo", response.getTradeNo());
                result.put("outTradeNo", response.getOutTradeNo());
                result.put("refundFee", response.getRefundFee());
                result.put("gmtRefundPay", response.getGmtRefundPay());
                log.info("申请退款成功，订单号：{}，退款金额：{}", outTradeNo, refundAmount);
            } else {
                result.put("success", false);
                result.put("errorMsg", response.getSubMsg());
                log.error("申请退款失败，订单号：{}，错误信息：{}", outTradeNo, response.getSubMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("申请退款异常，订单号：{}", outTradeNo, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMsg", "申请退款异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 查询退款状态
     */
    @Override
    public Map<String, Object> queryRefund(String outTradeNo, String outRequestNo)
    {
        try {
            AlipayClient alipayClient = getAlipayClient();
            AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();

            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", outTradeNo);
            bizContent.put("out_request_no", outRequestNo);

            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));

            AlipayTradeFastpayRefundQueryResponse response = alipayClient.execute(request);

            Map<String, Object> result = new HashMap<>();
            if (response.isSuccess()) {
                result.put("success", true);
                result.put("tradeNo", response.getTradeNo());
                result.put("outTradeNo", response.getOutTradeNo());
                result.put("refundAmount", response.getRefundAmount());
                result.put("refundStatus", response.getRefundStatus());
                log.info("查询退款状态成功，订单号：{}，退款状态：{}", outTradeNo, response.getRefundStatus());
            } else {
                result.put("success", false);
                result.put("errorMsg", response.getSubMsg());
                log.error("查询退款状态失败，订单号：{}，错误信息：{}", outTradeNo, response.getSubMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("查询退款状态异常，订单号：{}", outTradeNo, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMsg", "查询退款状态异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 单笔转账到支付宝账户（提现）
     */
    @Override
    public Map<String, Object> transfer(String outBizNo, String payeeAccount, BigDecimal amount, String payeeRealName, String remark)
    {
        try {
            AlipayClient alipayClient = getAlipayClient();
            AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();

            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_biz_no", outBizNo);
            bizContent.put("trans_amount", amount.toString());
            bizContent.put("product_code", "TRANS_ACCOUNT_NO_PWD");
            bizContent.put("biz_scene", "DIRECT_TRANSFER");
            bizContent.put("order_title", "提现转账");
            bizContent.put("remark", remark);

            // 收款方信息
            Map<String, Object> payeeInfo = new HashMap<>();
            payeeInfo.put("identity", payeeAccount);
            payeeInfo.put("identity_type", "ALIPAY_LOGON_ID");
            payeeInfo.put("name", payeeRealName);
            bizContent.put("payee_info", payeeInfo);

            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));

            AlipayFundTransUniTransferResponse response = alipayClient.execute(request);

            Map<String, Object> result = new HashMap<>();
            if (response.isSuccess()) {
                result.put("success", true);
                result.put("outBizNo", response.getOutBizNo());
                result.put("orderId", response.getOrderId());
                result.put("payFundOrderId", response.getPayFundOrderId());
                result.put("status", response.getStatus());
                result.put("transDate", response.getTransDate());
                log.info("提现转账成功，转账单号：{}，金额：{}", outBizNo, amount);
            } else {
                result.put("success", false);
                result.put("errorMsg", response.getSubMsg());
                log.error("提现转账失败，转账单号：{}，错误信息：{}", outBizNo, response.getSubMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("提现转账异常，转账单号：{}", outBizNo, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMsg", "提现转账异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 查询转账订单状态
     */
    @Override
    public Map<String, Object> queryTransfer(String outBizNo)
    {
        try {
            AlipayClient alipayClient = getAlipayClient();
            AlipayFundTransCommonQueryRequest request = new AlipayFundTransCommonQueryRequest();

            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_biz_no", outBizNo);
            bizContent.put("product_code", "TRANS_ACCOUNT_NO_PWD");
            bizContent.put("biz_scene", "DIRECT_TRANSFER");

            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));

            AlipayFundTransCommonQueryResponse response = alipayClient.execute(request);

            Map<String, Object> result = new HashMap<>();
            if (response.isSuccess()) {
                result.put("success", true);
                result.put("outBizNo", response.getOutBizNo());
                result.put("orderId", response.getOrderId());
                result.put("status", response.getStatus());
                result.put("payDate", response.getPayDate());
                result.put("arrivalTimeEnd", response.getArrivalTimeEnd());
                result.put("orderFee", response.getOrderFee());
                log.info("查询转账状态成功，转账单号：{}，状态：{}", outBizNo, response.getStatus());
            } else {
                result.put("success", false);
                result.put("errorMsg", response.getSubMsg());
                log.error("查询转账状态失败，转账单号：{}，错误信息：{}", outBizNo, response.getSubMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("查询转账状态异常，转账单号：{}", outBizNo, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMsg", "查询转账状态异常：" + e.getMessage());
            return result;
        }
    }
}
