package com.ruoyi.fuguang.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.fuguang.domain.OfflinePayment;
import org.apache.ibatis.annotations.Param;

/**
 * 线下支付订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface OfflinePaymentMapper 
{
    /**
     * 查询线下支付订单
     * 
     * @param paymentId 线下支付订单主键
     * @return 线下支付订单
     */
    public OfflinePayment selectOfflinePaymentByPaymentId(Long paymentId);

    /**
     * 根据订单号查询线下支付订单
     * 
     * @param orderNo 订单号
     * @return 线下支付订单
     */
    public OfflinePayment selectOfflinePaymentByOrderNo(String orderNo);

    /**
     * 查询线下支付订单列表
     * 
     * @param offlinePayment 线下支付订单
     * @return 线下支付订单集合
     */
    public List<OfflinePayment> selectOfflinePaymentList(OfflinePayment offlinePayment);

    /**
     * 新增线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    public int insertOfflinePayment(OfflinePayment offlinePayment);

    /**
     * 修改线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    public int updateOfflinePayment(OfflinePayment offlinePayment);

    /**
     * 删除线下支付订单
     * 
     * @param paymentId 线下支付订单主键
     * @return 结果
     */
    public int deleteOfflinePaymentByPaymentId(Long paymentId);

    /**
     * 批量删除线下支付订单
     *
     * @param paymentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOfflinePaymentByPaymentIds(Long[] paymentIds);

    // ==================== 统计查询方法 ====================

    /**
     * 获取支付总览统计
     *
     * @return 统计数据
     */
    public Map<String, Object> getPaymentOverviewStatistics();

    /**
     * 获取今日支付统计
     *
     * @return 今日统计数据
     */
    public Map<String, Object> getTodayPaymentStatistics();

    /**
     * 获取本月支付统计
     *
     * @return 本月统计数据
     */
    public Map<String, Object> getMonthPaymentStatistics();

    /**
     * 获取支付趋势统计
     *
     * @param days 天数
     * @return 趋势统计数据
     */
    public List<Map<String, Object>> getPaymentTrendStatistics(@Param("days") int days);

    /**
     * 获取支付渠道分布统计
     *
     * @return 渠道分布数据
     */
    public List<Map<String, Object>> getPaymentChannelDistribution();

    /**
     * 获取支付异常统计
     *
     * @return 异常统计数据
     */
    public Map<String, Object> getPaymentExceptionStatistics();

    /**
     * 获取支付成功率统计
     *
     * @param days 天数
     * @return 成功率统计数据
     */
    public List<Map<String, Object>> getPaymentSuccessRateStatistics(@Param("days") int days);

    /**
     * 获取支付金额分布统计
     *
     * @return 金额分布数据
     */
    public List<Map<String, Object>> getPaymentAmountDistribution();

    /**
     * 获取用户支付行为分析
     *
     * @return 用户行为分析数据
     */
    public Map<String, Object> getUserPaymentBehaviorAnalysis();

    /**
     * 获取支付时段分析
     *
     * @return 时段分析数据
     */
    public List<Map<String, Object>> getPaymentTimeAnalysis();

    /**
     * 获取退款统计
     *
     * @return 退款统计数据
     */
    public Map<String, Object> getRefundStatistics();

    /**
     * 获取转账统计
     *
     * @return 转账统计数据
     */
    public Map<String, Object> getTransferStatistics();
}
