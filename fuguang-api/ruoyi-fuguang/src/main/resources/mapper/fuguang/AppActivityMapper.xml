<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppActivityMapper">
    
    <resultMap type="AppActivity" id="AppActivityResult">
        <result property="activityId"    column="activity_id"    />
        <result property="activityName"    column="activity_name"    />
        <result property="activityImage"    column="activity_image"    />
        <result property="activityUrl"    column="activity_url"    />
        <result property="activityDesc"    column="activity_desc"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAppActivityVo">
        select activity_id, activity_name, activity_image, activity_url, activity_desc, start_time, end_time, sort_order, status, create_by, create_time, update_by, update_time, remark from app_activity
    </sql>

    <select id="selectAppActivityList" parameterType="AppActivity" resultMap="AppActivityResult">
        <include refid="selectAppActivityVo"/>
        <where>
            <if test="activityName != null  and activityName != ''"> and activity_name like concat('%', #{activityName}, '%')</if>
            <if test="activityUrl != null  and activityUrl != ''"> and activity_url = #{activityUrl}</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectAppActivityByActivityId" parameterType="Long" resultMap="AppActivityResult">
        <include refid="selectAppActivityVo"/>
        where activity_id = #{activityId}
    </select>



    <select id="selectValidAppActivityList" resultMap="AppActivityResult">
        <include refid="selectAppActivityVo"/>
        where status = '0' 
        and (start_time is null or start_time &lt;= now())
        and (end_time is null or end_time &gt;= now())
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertAppActivity" parameterType="AppActivity" useGeneratedKeys="true" keyProperty="activityId">
        insert into app_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityName != null and activityName != ''">activity_name,</if>
            <if test="activityImage != null">activity_image,</if>
            <if test="activityUrl != null">activity_url,</if>
            <if test="activityDesc != null">activity_desc,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityName != null and activityName != ''">#{activityName},</if>
            <if test="activityImage != null">#{activityImage},</if>
            <if test="activityUrl != null">#{activityUrl},</if>
            <if test="activityDesc != null">#{activityDesc},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppActivity" parameterType="AppActivity">
        update app_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityName != null and activityName != ''">activity_name = #{activityName},</if>
            <if test="activityImage != null">activity_image = #{activityImage},</if>
            <if test="activityUrl != null">activity_url = #{activityUrl},</if>
            <if test="activityDesc != null">activity_desc = #{activityDesc},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where activity_id = #{activityId}
    </update>

    <delete id="deleteAppActivityByActivityId" parameterType="Long">
        delete from app_activity where activity_id = #{activityId}
    </delete>

    <delete id="deleteAppActivityByActivityIds" parameterType="String">
        delete from app_activity where activity_id in 
        <foreach item="activityId" collection="array" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </delete>
</mapper>
