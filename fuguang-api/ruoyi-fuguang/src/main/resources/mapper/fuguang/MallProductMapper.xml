<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MallProductMapper">

    <resultMap type="MallProduct" id="MallProductResult">
        <result property="productId"    column="product_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productDesc"    column="product_desc"    />
        <result property="productImage"    column="product_image"    />
        <result property="productImages"    column="product_images"    />
        <result property="originalPrice"    column="original_price"    />
        <result property="salePrice"    column="sale_price"    />
        <result property="stockQuantity"    column="stock_quantity"    />
        <result property="salesCount"    column="sales_count"    />
        <result property="productStatus"    column="product_status"    />
        <result property="isHot"    column="is_hot"    />
        <result property="isNew"    column="is_new"    />
        <result property="isRecommend"    column="is_recommend"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="categoryName"    column="category_name"    />
    </resultMap>

    <sql id="selectMallProductVo">
        select p.product_id, p.category_id, p.product_name, p.product_desc, p.product_image, p.product_images,
               p.original_price, p.sale_price, p.stock_quantity, p.sales_count, p.product_status, p.is_hot,
               p.is_new, p.is_recommend, p.sort_order, p.del_flag, p.create_by, p.create_time, p.update_by,
               p.update_time, p.remark, c.category_name
        from mall_product p
        left join mall_category c on p.category_id = c.category_id
    </sql>

    <select id="selectMallProductList" parameterType="MallProduct" resultMap="MallProductResult">
        <include refid="selectMallProductVo"/>
        <where>
            p.del_flag = '0'
            <if test="categoryId != null "> and p.category_id = #{categoryId}</if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productStatus != null  and productStatus != ''"> and p.product_status = #{productStatus}</if>
            <if test="isHot != null  and isHot != ''"> and p.is_hot = #{isHot}</if>
            <if test="isNew != null  and isNew != ''"> and p.is_new = #{isNew}</if>
            <if test="isRecommend != null  and isRecommend != ''"> and p.is_recommend = #{isRecommend}</if>
        </where>
        order by p.sort_order asc, p.create_time desc
    </select>

    <select id="selectMallProductByProductId" parameterType="Long" resultMap="MallProductResult">
        <include refid="selectMallProductVo"/>
        where p.product_id = #{productId} and p.del_flag = '0'
    </select>

    <select id="selectHotProductList" parameterType="Integer" resultMap="MallProductResult">
        <include refid="selectMallProductVo"/>
        where p.del_flag = '0' and p.product_status = '0' and p.is_hot = '1'
        order by p.sort_order asc, p.sales_count desc, p.create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectNewProductList" parameterType="Integer" resultMap="MallProductResult">
        <include refid="selectMallProductVo"/>
        where p.del_flag = '0' and p.product_status = '0' and p.is_new = '1'
        order by p.sort_order asc, p.create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectRecommendProductList" parameterType="Integer" resultMap="MallProductResult">
        <include refid="selectMallProductVo"/>
        where p.del_flag = '0' and p.product_status = '0' and p.is_recommend = '1'
        order by p.sort_order asc, p.sales_count desc, p.create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectProductListByCategoryId" resultMap="MallProductResult">
        <include refid="selectMallProductVo"/>
        where p.del_flag = '0' and p.product_status = '0' and p.category_id = #{categoryId}
        order by p.sort_order asc, p.create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="searchProducts" resultMap="MallProductResult">
        <include refid="selectMallProductVo"/>
        where p.del_flag = '0' and p.product_status = '0'
        and (p.product_name like concat('%', #{keyword}, '%') or p.product_desc like concat('%', #{keyword}, '%'))
        order by p.sort_order asc, p.sales_count desc, p.create_time desc
    </select>

    <insert id="insertMallProduct" parameterType="MallProduct" useGeneratedKeys="true" keyProperty="productId">
        insert into mall_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productDesc != null">product_desc,</if>
            <if test="productImage != null">product_image,</if>
            <if test="productImages != null">product_images,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="salePrice != null">sale_price,</if>
            <if test="stockQuantity != null">stock_quantity,</if>
            <if test="salesCount != null">sales_count,</if>
            <if test="productStatus != null">product_status,</if>
            <if test="isHot != null">is_hot,</if>
            <if test="isNew != null">is_new,</if>
            <if test="isRecommend != null">is_recommend,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productDesc != null">#{productDesc},</if>
            <if test="productImage != null">#{productImage},</if>
            <if test="productImages != null">#{productImages},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="salePrice != null">#{salePrice},</if>
            <if test="stockQuantity != null">#{stockQuantity},</if>
            <if test="salesCount != null">#{salesCount},</if>
            <if test="productStatus != null">#{productStatus},</if>
            <if test="isHot != null">#{isHot},</if>
            <if test="isNew != null">#{isNew},</if>
            <if test="isRecommend != null">#{isRecommend},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMallProduct" parameterType="MallProduct">
        update mall_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productDesc != null">product_desc = #{productDesc},</if>
            <if test="productImage != null">product_image = #{productImage},</if>
            <if test="productImages != null">product_images = #{productImages},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="salePrice != null">sale_price = #{salePrice},</if>
            <if test="stockQuantity != null">stock_quantity = #{stockQuantity},</if>
            <if test="salesCount != null">sales_count = #{salesCount},</if>
            <if test="productStatus != null">product_status = #{productStatus},</if>
            <if test="isHot != null">is_hot = #{isHot},</if>
            <if test="isNew != null">is_new = #{isNew},</if>
            <if test="isRecommend != null">is_recommend = #{isRecommend},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where product_id = #{productId}
    </update>

    <delete id="deleteMallProductByProductId" parameterType="Long">
        update mall_product set del_flag = '2' where product_id = #{productId}
    </delete>

    <delete id="deleteMallProductByProductIds" parameterType="String">
        update mall_product set del_flag = '2' where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>

    <update id="updateProductStock">
        update mall_product
        set stock_quantity = stock_quantity + #{quantity}
        where product_id = #{productId} and del_flag = '0'
    </update>

    <update id="updateProductSales">
        update mall_product
        set sales_count = sales_count + #{quantity}
        where product_id = #{productId} and del_flag = '0'
    </update>

    <select id="checkProductNameUnique" parameterType="String" resultMap="MallProductResult">
        <include refid="selectMallProductVo"/>
        where p.product_name = #{productName} and p.del_flag = '0' limit 1
    </select>

</mapper>