<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.PaymentCallbackLogMapper">
    
    <resultMap type="PaymentCallbackLog" id="PaymentCallbackLogResult">
        <result property="callbackId"    column="callback_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="businessType"    column="business_type"    />
        <result property="payType"    column="pay_type"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="callbackStatus"    column="callback_status"    />
        <result property="callbackParams"    column="callback_params"    />
        <result property="callbackTime"    column="callback_time"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPaymentCallbackLogVo">
        select callback_id, order_no, business_type, pay_type, pay_amount, callback_status, callback_params, callback_time, retry_count, error_msg, create_time, update_time from payment_callback_log
    </sql>

    <select id="selectPaymentCallbackLogList" parameterType="PaymentCallbackLog" resultMap="PaymentCallbackLogResult">
        <include refid="selectPaymentCallbackLogVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="callbackStatus != null  and callbackStatus != ''"> and callback_status = #{callbackStatus}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(callback_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(callback_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by callback_time desc
    </select>
    
    <select id="selectPaymentCallbackLogByCallbackId" parameterType="Long" resultMap="PaymentCallbackLogResult">
        <include refid="selectPaymentCallbackLogVo"/>
        where callback_id = #{callbackId}
    </select>

    <select id="selectPaymentCallbackLogByOrderNo" parameterType="String" resultMap="PaymentCallbackLogResult">
        <include refid="selectPaymentCallbackLogVo"/>
        where order_no = #{orderNo}
        order by callback_time desc
    </select>
        
    <insert id="insertPaymentCallbackLog" parameterType="PaymentCallbackLog" useGeneratedKeys="true" keyProperty="callbackId">
        insert into payment_callback_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="payType != null and payType != ''">pay_type,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="callbackStatus != null">callback_status,</if>
            <if test="callbackParams != null">callback_params,</if>
            <if test="callbackTime != null">callback_time,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="payType != null and payType != ''">#{payType},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="callbackStatus != null">#{callbackStatus},</if>
            <if test="callbackParams != null">#{callbackParams},</if>
            <if test="callbackTime != null">#{callbackTime},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePaymentCallbackLog" parameterType="PaymentCallbackLog">
        update payment_callback_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="payType != null and payType != ''">pay_type = #{payType},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="callbackStatus != null">callback_status = #{callbackStatus},</if>
            <if test="callbackParams != null">callback_params = #{callbackParams},</if>
            <if test="callbackTime != null">callback_time = #{callbackTime},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where callback_id = #{callbackId}
    </update>

    <delete id="deletePaymentCallbackLogByCallbackId" parameterType="Long">
        delete from payment_callback_log where callback_id = #{callbackId}
    </delete>

    <delete id="deletePaymentCallbackLogByCallbackIds" parameterType="String">
        delete from payment_callback_log where callback_id in 
        <foreach item="callbackId" collection="array" open="(" separator="," close=")">
            #{callbackId}
        </foreach>
    </delete>

    <!-- 获取回调统计信息 -->
    <select id="getCallbackStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalCount,
            SUM(CASE WHEN callback_status = '1' THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN callback_status = '0' THEN 1 ELSE 0 END) as failedCount,
            ROUND(SUM(CASE WHEN callback_status = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as successRate
        FROM payment_callback_log
    </select>

    <!-- 获取今日回调统计 -->
    <select id="getTodayCallbackStatistics" resultType="map">
        SELECT 
            COUNT(*) as todayTotal,
            SUM(CASE WHEN callback_status = '1' THEN 1 ELSE 0 END) as todaySuccess,
            SUM(CASE WHEN callback_status = '0' THEN 1 ELSE 0 END) as todayFailed,
            ROUND(SUM(CASE WHEN callback_status = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as todaySuccessRate
        FROM payment_callback_log
        WHERE DATE(callback_time) = CURDATE()
    </select>

    <!-- 获取回调成功率统计（按业务类型） -->
    <select id="getCallbackSuccessRateByBusinessType" resultType="map">
        SELECT 
            business_type as businessType,
            COUNT(*) as totalCount,
            SUM(CASE WHEN callback_status = '1' THEN 1 ELSE 0 END) as successCount,
            ROUND(SUM(CASE WHEN callback_status = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as successRate
        FROM payment_callback_log
        GROUP BY business_type
    </select>

    <!-- 获取回调趋势统计（最近7天） -->
    <select id="getCallbackTrendStatistics" resultType="map">
        SELECT 
            DATE(callback_time) as callbackDate,
            COUNT(*) as totalCount,
            SUM(CASE WHEN callback_status = '1' THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN callback_status = '0' THEN 1 ELSE 0 END) as failedCount
        FROM payment_callback_log
        WHERE callback_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(callback_time)
        ORDER BY callbackDate
    </select>

    <!-- 更新重试次数 -->
    <update id="updateRetryCount" parameterType="Long">
        update payment_callback_log 
        set retry_count = retry_count + 1, update_time = now()
        where callback_id = #{callbackId}
    </update>

</mapper>
