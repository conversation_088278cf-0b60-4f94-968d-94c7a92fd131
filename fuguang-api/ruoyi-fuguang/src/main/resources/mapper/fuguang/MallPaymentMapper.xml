<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MallPaymentMapper">

    <resultMap type="MallPayment" id="MallPaymentResult">
        <id     property="paymentId"    column="payment_id"    />
        <result property="orderId"      column="order_id"      />
        <result property="orderNo"      column="order_no"      />
        <result property="userId"       column="user_id"       />
        <result property="payAmount"    column="pay_amount"    />
        <result property="payType"      column="pay_type"      />
        <result property="payStatus"    column="pay_status"    />
        <result property="tradeNo"      column="trade_no"      />
        <result property="payTime"      column="pay_time"      />
        <result property="notifyTime"   column="notify_time"   />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
    </resultMap>

    <sql id="selectMallPaymentVo">
        select payment_id, order_id, order_no, user_id, pay_amount, pay_type, pay_status, trade_no, pay_time, notify_time, create_time, update_time from mall_payment
    </sql>

    <select id="selectMallPaymentList" parameterType="MallPayment" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        <where>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="payStatus != null  and payStatus != ''"> and pay_status = #{payStatus}</if>
            <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectMallPaymentByPaymentId" parameterType="Long" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        where payment_id = #{paymentId}
    </select>

    <select id="selectPaymentByOrderId" parameterType="Long" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        where order_id = #{orderId}
    </select>

    <select id="selectPaymentByOrderNo" parameterType="String" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        where order_no = #{orderNo}
    </select>

    <select id="selectPaymentByTradeNo" parameterType="String" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        where trade_no = #{tradeNo}
    </select>

    <insert id="insertMallPayment" parameterType="MallPayment" useGeneratedKeys="true" keyProperty="paymentId">
        insert into mall_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="notifyTime != null">notify_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="notifyTime != null">#{notifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMallPayment" parameterType="MallPayment">
        update mall_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="notifyTime != null">notify_time = #{notifyTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where payment_id = #{paymentId}
    </update>

    <update id="updatePaymentStatus">
        update mall_payment
        <trim prefix="SET" suffixOverrides=",">
            pay_status = #{payStatus},
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="payStatus == '1'">pay_time = now(),</if>
            notify_time = now(),
            update_time = now(),
        </trim>
        where payment_id = #{paymentId}
    </update>

    <update id="updatePaymentStatusByOrderNo">
        update mall_payment
        <trim prefix="SET" suffixOverrides=",">
            pay_status = #{payStatus},
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="payStatus == '1'">pay_time = now(),</if>
            notify_time = now(),
            update_time = now(),
        </trim>
        where order_no = #{orderNo}
    </update>

    <delete id="deleteMallPaymentByPaymentId" parameterType="Long">
        delete from mall_payment where payment_id = #{paymentId}
    </delete>

    <delete id="deleteMallPaymentByPaymentIds" parameterType="String">
        delete from mall_payment where payment_id in
        <foreach item="paymentId" collection="array" open="(" separator="," close=")">
            #{paymentId}
        </foreach>
    </delete>

    <!-- ==================== 统计查询SQL ==================== -->

    <!-- 获取支付总览统计 -->
    <select id="getPaymentOverviewStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalCount,
            COALESCE(SUM(pay_amount), 0) as totalAmount,
            COUNT(CASE WHEN pay_status = '1' THEN 1 END) as successCount,
            COALESCE(SUM(CASE WHEN pay_status = '1' THEN pay_amount ELSE 0 END), 0) as successAmount,
            COUNT(CASE WHEN pay_status = '2' THEN 1 END) as failCount,
            COUNT(CASE WHEN pay_status = '3' THEN 1 END) as refundCount,
            COALESCE(SUM(CASE WHEN pay_status = '3' THEN pay_amount ELSE 0 END), 0) as refundAmount
        FROM mall_payment
    </select>

    <!-- 获取今日支付统计 -->
    <select id="getTodayPaymentStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as todayCount,
            COALESCE(SUM(pay_amount), 0) as todayAmount,
            COUNT(CASE WHEN pay_status = '1' THEN 1 END) as todaySuccessCount,
            COALESCE(SUM(CASE WHEN pay_status = '1' THEN pay_amount ELSE 0 END), 0) as todaySuccessAmount,
            ROUND(COUNT(CASE WHEN pay_status = '1' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as todaySuccessRate
        FROM mall_payment
        WHERE DATE(create_time) = CURDATE()
    </select>

    <!-- 获取本月支付统计 -->
    <select id="getMonthPaymentStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as monthCount,
            COALESCE(SUM(pay_amount), 0) as monthAmount,
            COUNT(CASE WHEN pay_status = '1' THEN 1 END) as monthSuccessCount,
            COALESCE(SUM(CASE WHEN pay_status = '1' THEN pay_amount ELSE 0 END), 0) as monthSuccessAmount,
            ROUND(COUNT(CASE WHEN pay_status = '1' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as monthSuccessRate
        FROM mall_payment
        WHERE YEAR(create_time) = YEAR(CURDATE()) AND MONTH(create_time) = MONTH(CURDATE())
    </select>

    <!-- 获取支付趋势统计 -->
    <select id="getPaymentTrendStatistics" resultType="java.util.Map">
        SELECT
            DATE(create_time) as date,
            COUNT(*) as count,
            COALESCE(SUM(pay_amount), 0) as amount,
            COUNT(CASE WHEN pay_status = '1' THEN 1 END) as successCount,
            COALESCE(SUM(CASE WHEN pay_status = '1' THEN pay_amount ELSE 0 END), 0) as successAmount
        FROM mall_payment
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

    <!-- 获取支付渠道分布统计 -->
    <select id="getPaymentChannelDistribution" resultType="java.util.Map">
        SELECT
            pay_type as payType,
            CASE pay_type
                WHEN '1' THEN '支付宝'
                WHEN '2' THEN '微信'
                WHEN '3' THEN '余额'
                ELSE '其他'
            END as payTypeName,
            COUNT(*) as count,
            COALESCE(SUM(pay_amount), 0) as amount,
            COUNT(CASE WHEN pay_status = '1' THEN 1 END) as successCount,
            ROUND(COUNT(CASE WHEN pay_status = '1' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as successRate
        FROM mall_payment
        GROUP BY pay_type
        ORDER BY count DESC
    </select>

    <!-- 获取支付异常统计 -->
    <select id="getPaymentExceptionStatistics" resultType="java.util.Map">
        SELECT
            COUNT(CASE WHEN pay_status = '2' THEN 1 END) as failCount,
            COALESCE(SUM(CASE WHEN pay_status = '2' THEN pay_amount ELSE 0 END), 0) as failAmount,
            COUNT(CASE WHEN pay_status = '0' AND create_time &lt; DATE_SUB(NOW(), INTERVAL 30 MINUTE) THEN 1 END) as timeoutCount,
            COUNT(CASE WHEN pay_status = '3' THEN 1 END) as refundCount,
            COALESCE(SUM(CASE WHEN pay_status = '3' THEN pay_amount ELSE 0 END), 0) as refundAmount
        FROM mall_payment
    </select>

    <!-- 获取支付成功率统计 -->
    <select id="getPaymentSuccessRateStatistics" resultType="java.util.Map">
        SELECT
            DATE(create_time) as date,
            COUNT(*) as totalCount,
            COUNT(CASE WHEN pay_status = '1' THEN 1 END) as successCount,
            ROUND(COUNT(CASE WHEN pay_status = '1' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as successRate
        FROM mall_payment
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

    <!-- 获取支付金额分布统计 -->
    <select id="getPaymentAmountDistribution" resultType="java.util.Map">
        SELECT
            CASE
                WHEN pay_amount &lt; 10 THEN '0-10元'
                WHEN pay_amount &lt; 50 THEN '10-50元'
                WHEN pay_amount &lt; 100 THEN '50-100元'
                WHEN pay_amount &lt; 500 THEN '100-500元'
                WHEN pay_amount &lt; 1000 THEN '500-1000元'
                ELSE '1000元以上'
            END as amountRange,
            COUNT(*) as count,
            COALESCE(SUM(pay_amount), 0) as totalAmount
        FROM mall_payment
        WHERE pay_status = '1'
        GROUP BY
            CASE
                WHEN pay_amount &lt; 10 THEN '0-10元'
                WHEN pay_amount &lt; 50 THEN '10-50元'
                WHEN pay_amount &lt; 100 THEN '50-100元'
                WHEN pay_amount &lt; 500 THEN '100-500元'
                WHEN pay_amount &lt; 1000 THEN '500-1000元'
                ELSE '1000元以上'
            END
        ORDER BY MIN(pay_amount)
    </select>

    <!-- 获取用户支付行为分析 -->
    <select id="getUserPaymentBehaviorAnalysis" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT user_id) as totalUsers,
            ROUND(AVG(pay_amount), 2) as avgPayAmount,
            MAX(pay_amount) as maxPayAmount,
            MIN(pay_amount) as minPayAmount,
            ROUND(COUNT(*) * 1.0 / COUNT(DISTINCT user_id), 2) as avgPayCountPerUser
        FROM mall_payment
        WHERE pay_status = '1'
    </select>

    <!-- 获取支付时段分析 -->
    <select id="getPaymentTimeAnalysis" resultType="java.util.Map">
        SELECT
            HOUR(pay_time) as hour,
            COUNT(*) as count,
            COALESCE(SUM(pay_amount), 0) as amount
        FROM mall_payment
        WHERE pay_status = '1' AND pay_time IS NOT NULL
        GROUP BY HOUR(pay_time)
        ORDER BY hour
    </select>

    <!-- 获取退款统计 -->
    <select id="getRefundStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as refundCount,
            COALESCE(SUM(pay_amount), 0) as refundAmount,
            COUNT(CASE WHEN DATE(update_time) = CURDATE() THEN 1 END) as todayRefundCount,
            COALESCE(SUM(CASE WHEN DATE(update_time) = CURDATE() THEN pay_amount ELSE 0 END), 0) as todayRefundAmount
        FROM mall_payment
        WHERE pay_status = '3'
    </select>

</mapper>