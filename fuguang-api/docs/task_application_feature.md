# 任务申请功能说明文档

## 功能概述

本功能实现了APP用户发布任务后，其他用户可以申请接取该任务，发布者可以从申请列表中选择合适的接取人的完整流程。

## 功能特点

1. **任务申请流程**：用户可以申请感兴趣的任务，避免抢单模式的不公平性
2. **发布者选择权**：任务发布者可以查看所有申请，选择最合适的接取人
3. **状态管理**：新增"待确认接取人"状态，完善任务状态流转
4. **权限控制**：严格的权限控制，确保操作安全性
5. **管理后台支持**：管理员可以查看和管理所有任务申请

## 数据库变更

### 新增表结构

```sql
-- 任务申请表
CREATE TABLE `app_task_application` (
  `application_id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `applicant_id` bigint NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(30) DEFAULT '' COMMENT '申请人昵称',
  `applicant_avatar` varchar(100) DEFAULT '' COMMENT '申请人头像',
  `application_reason` varchar(500) DEFAULT '' COMMENT '申请理由',
  `application_status` char(1) DEFAULT '0' COMMENT '申请状态（0待确认 1已确认 2已拒绝 3已取消）',
  `create_time` datetime DEFAULT NULL COMMENT '申请时间',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`application_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_applicant_id` (`applicant_id`),
  KEY `idx_application_status` (`application_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP任务申请表';
```

### 任务状态更新

新增任务状态：`6-待确认接取人`

## API接口

### APP端接口

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/app/task/apply/{taskId}` | POST | 申请任务 |
| `/app/task/applications/my` | GET | 查询我的申请列表 |
| `/app/task/applications/{taskId}` | GET | 查询任务申请列表（发布者） |
| `/app/task/applications/confirm/{applicationId}` | POST | 确认申请 |
| `/app/task/applications/reject/{applicationId}` | POST | 拒绝申请 |
| `/app/task/applications/cancel/{applicationId}` | POST | 取消申请 |
| `/app/task/applications/check/{taskId}` | GET | 检查申请状态 |

### 管理后台接口

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/fuguang/taskApplication/list` | GET | 查询申请列表 |
| `/fuguang/taskApplication/{applicationId}` | GET | 查询申请详情 |
| `/fuguang/taskApplication/confirm/{applicationId}` | PUT | 管理员确认申请 |
| `/fuguang/taskApplication/reject/{applicationId}` | PUT | 管理员拒绝申请 |

## 业务流程

1. **任务发布**：用户发布任务，状态为"1-待接取"
2. **用户申请**：其他用户可以申请该任务，申请后任务状态变为"6-待确认接取人"
3. **查看申请**：发布者可以查看所有申请列表，包含申请人信息和申请理由
4. **确认接取人**：发布者选择一个申请者确认，任务状态变为"2-进行中"
5. **自动拒绝**：确认一个申请后，其他申请自动变为"已拒绝"状态

## 部署说明

### 1. 数据库更新

执行以下SQL文件：
- `fuguang-api/sql/fuguang.sql` - 包含完整的数据库结构
- `fuguang-api/sql/task_application_menu.sql` - 管理后台菜单配置

### 2. 后端部署

确保以下文件已部署：
- 实体类：`AppTaskApplication.java`
- 数据访问层：`AppTaskApplicationMapper.java` 和对应的XML文件
- 业务逻辑层：`IAppTaskApplicationService.java` 和实现类
- 控制器：`AppTaskController.java`（更新）和 `AppTaskApplicationManageController.java`

### 3. 前端部署

确保以下文件已部署：
- API文件：`fuguang-web/src/api/fuguang/taskApplication.js`
- 管理页面：`fuguang-web/src/views/fuguang/taskApplication/index.vue`
- 更新的任务管理页面：`fuguang-web/src/views/fuguang/task/index.vue`

### 4. 菜单配置

1. 执行 `task_application_menu.sql` 添加管理后台菜单
2. 根据实际情况调整菜单的父级ID
3. 为相关角色分配菜单权限

## 权限说明

### APP端权限

- 任何用户都可以申请任务（除了任务发布者本人）
- 只有任务发布者可以查看申请列表和确认/拒绝申请
- 只有申请人可以取消自己的申请

### 管理后台权限

- `fuguang:taskApplication:list` - 查看申请列表
- `fuguang:taskApplication:query` - 查看申请详情
- `fuguang:taskApplication:confirm` - 确认申请
- `fuguang:taskApplication:reject` - 拒绝申请
- `fuguang:taskApplication:add/edit/remove` - 基础CRUD操作

## 注意事项

1. **状态一致性**：确保任务状态和申请状态的一致性
2. **并发控制**：多个用户同时申请时的并发处理
3. **权限验证**：严格验证用户权限，防止越权操作
4. **数据完整性**：确保申请记录和任务记录的关联完整性

## 测试建议

1. **功能测试**：测试完整的申请-确认流程
2. **权限测试**：测试各种权限边界情况
3. **并发测试**：测试多用户同时申请的情况
4. **异常测试**：测试各种异常情况的处理

## 后续优化建议

1. **消息通知**：添加申请状态变更的消息通知
2. **申请限制**：可以设置每个任务的最大申请人数
3. **申请评分**：根据用户历史表现对申请进行评分排序
4. **自动确认**：设置自动确认规则，如先到先得等
