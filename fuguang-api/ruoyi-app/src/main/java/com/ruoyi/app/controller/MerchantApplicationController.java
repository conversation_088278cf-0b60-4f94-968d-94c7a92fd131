package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.fuguang.service.IOfflinePaymentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.MerchantApplication;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.config.RuoYiConfig;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.RequestParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP商家申请管理控制器
 * 提供商家申请提交、查询、资质上传等功能
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Api(tags = "APP商家申请管理", description = "APP商家申请相关接口")
@RestController("merchantApplicationApiController")
@RequestMapping("/app/merchant")
public class MerchantApplicationController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(MerchantApplicationController.class);

    @Autowired
    private IMerchantApplicationService merchantApplicationService;

    @Autowired
    private IOfflinePaymentService offlinePaymentService;


    /**
     * 查询当前用户的商家申请
     * 获取当前用户的商家申请记录和审核状态
     *
     * @return 商家申请信息
     */
    @ApiOperation(value = "查询当前用户的商家申请",
                  notes = "获取当前用户的商家申请记录，包括申请状态、审核意见等信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回申请信息（可能为空）")
    })
    @GetMapping("/application")
    public AjaxResult getMyApplication()
    {
        Long userId = getUserId();
        MerchantApplication application = merchantApplicationService.selectMerchantApplicationByUserId(userId);
        return success(application);
    }

    /**
     * 检查用户是否可以申请成为商家
     * 验证用户是否满足申请商家的条件
     *
     * @return 是否可以申请
     */
    @ApiOperation(value = "检查用户是否可以申请成为商家",
                  notes = "检查当前用户是否满足申请商家的条件，如实名认证、无待审核申请等")
    @ApiResponses({
        @ApiResponse(code = 200, message = "检查完成，返回是否可以申请")
    })
    @GetMapping("/checkCanApply")
    public AjaxResult checkCanApply()
    {
        Long userId = getUserId();
        boolean canApply = merchantApplicationService.checkUserCanApply(userId);
        return success(canApply);
    }

    /**
     * 提交商家申请
     * 用户提交商家申请，包含店铺信息和资质材料
     *
     * @param merchantApplication 商家申请信息
     * @return 申请结果
     */
    @ApiOperation(value = "提交商家申请",
                  notes = "用户提交商家申请，需要填写店铺名称、经营类目、联系方式等信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "申请提交成功"),
        @ApiResponse(code = 500, message = "申请失败，不满足申请条件或信息不完整")
    })
    @Log(title = "商家申请", businessType = BusinessType.INSERT)
    @PostMapping("/apply")
    public AjaxResult submitApplication(@ApiParam(value = "商家申请信息", required = true) @RequestBody MerchantApplication merchantApplication)
    {
        Long userId = getUserId();
        merchantApplication.setUserId(userId);

        // 检查用户是否可以申请
        if (!merchantApplicationService.checkUserCanApply(userId))
        {
            return error("您已经是商家用户或已有待审核的申请，无法重复申请");
        }
        
        // 数据校验
        String validateResult = merchantApplicationService.validateApplicationData(merchantApplication);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        merchantApplication.setCreateBy(String.valueOf(userId));
        boolean result = merchantApplicationService.submitApplication(merchantApplication);
        
        if (result)
        {
            return success("申请提交成功，请等待审核");
        }
        return error("申请提交失败");
    }

    /**
     * 修改商家申请（仅限待审核状态）
     */
    @ApiOperation("修改商家申请")
    @Log(title = "商家申请", businessType = BusinessType.UPDATE)
    @PutMapping("/application")
    public AjaxResult updateApplication(@RequestBody MerchantApplication merchantApplication)
    {
        Long userId = getUserId();
        
        // 检查申请是否存在且属于当前用户
        MerchantApplication existApplication = merchantApplicationService.selectMerchantApplicationByUserId(userId);
        if (existApplication == null)
        {
            return error("申请不存在");
        }
        
        // 只有待审核状态的申请才能修改
        if (!"0".equals(existApplication.getApplicationStatus()))
        {
            return error("只有待审核状态的申请才能修改");
        }
        
        merchantApplication.setApplicationId(existApplication.getApplicationId());
        merchantApplication.setUserId(userId);
        
        // 数据校验
        String validateResult = merchantApplicationService.validateApplicationData(merchantApplication);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        merchantApplication.setUpdateBy(String.valueOf(userId));
        int result = merchantApplicationService.updateMerchantApplication(merchantApplication);
        
        if (result > 0)
        {
            return success("申请修改成功");
        }
        return error("申请修改失败");
    }

    /**
     * 撤销商家申请（仅限待审核状态）
     */
    @ApiOperation("撤销商家申请")
    @Log(title = "商家申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/application")
    public AjaxResult cancelApplication()
    {
        Long userId = getUserId();
        
        // 检查申请是否存在且属于当前用户
        MerchantApplication existApplication = merchantApplicationService.selectMerchantApplicationByUserId(userId);
        if (existApplication == null)
        {
            return error("申请不存在");
        }
        
        // 只有待审核状态的申请才能撤销
        if (!"0".equals(existApplication.getApplicationStatus()))
        {
            return error("只有待审核状态的申请才能撤销");
        }
        
        int result = merchantApplicationService.deleteMerchantApplicationByApplicationId(existApplication.getApplicationId());
        
        if (result > 0)
        {
            return success("申请撤销成功");
        }
        return error("申请撤销失败");
    }

    /**
     * 获取申请状态说明
     */
    @ApiOperation("获取申请状态说明")
    @GetMapping("/statusInfo")
    public AjaxResult getStatusInfo()
    {
        return success("申请状态说明", new String[]{
            "0: 待审核 - 您的申请已提交，请耐心等待管理员审核",
            "1: 审核通过 - 恭喜您成为商家用户，可以开始发布商品",
            "2: 审核拒绝 - 很抱歉，您的申请未通过审核，请查看审核意见后重新申请"
        });
    }


}
