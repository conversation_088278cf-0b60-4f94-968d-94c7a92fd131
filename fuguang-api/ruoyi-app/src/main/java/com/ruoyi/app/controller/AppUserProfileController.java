package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.fuguang.domain.AppUserProfile;
import com.ruoyi.fuguang.domain.AppUserTimeline;
import com.ruoyi.fuguang.service.IAppUserProfileService;
import com.ruoyi.fuguang.service.IAppUserTimelineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP用户个人简介API控制器
 * 提供个人简介管理、图片视频上传、履历时间线查询等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP用户个人简介", description = "APP用户个人简介管理相关接口")
@RestController("appUserProfileApiController")
@RequestMapping("/app/profile")
public class AppUserProfileController extends BaseController
{
    @Autowired
    private IAppUserProfileService appUserProfileService;

    @Autowired
    private IAppUserTimelineService appUserTimelineService;

    /**
     * 获取当前用户个人简介
     * 包含用户基本信息、信用分、任务分、履历等
     *
     * @return 用户个人简介信息
     */
    @ApiOperation(value = "获取个人简介",
                  notes = "获取当前登录用户的个人简介信息，包括信用分、任务分、扶贫救援标志等")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回个人简介信息"),
        @ApiResponse(code = 500, message = "个人简介不存在")
    })
    @GetMapping("/info")
    public AjaxResult getProfile(@ApiParam(value = "用户主键，为空则查询当前登录人", required = false)@RequestParam(value = "userId",required = false)Long userId)
    {
        if(StringUtils.isNull(userId)){
            userId = getUserId();
        }
        AppUserProfile profile = appUserProfileService.selectAppUserProfileByUserId(userId);
        
        if (profile == null) {
            // 如果不存在，初始化个人简介
            boolean initResult = appUserProfileService.initUserProfile(userId);
            if (initResult) {
                profile = appUserProfileService.selectAppUserProfileByUserId(userId);
            } else {
                return error("个人简介初始化失败");
            }
        }

        return success(profile);
    }

    /**
     * 更新个人简介
     * 用户可以更新个人描述、图片等信息
     *
     * @param appUserProfile 个人简介信息
     * @return 更新结果
     */
    @ApiOperation(value = "更新个人简介",
                  notes = "更新用户个人简介信息，包括个人描述、图片等")
    @ApiResponses({
        @ApiResponse(code = 200, message = "更新成功"),
        @ApiResponse(code = 500, message = "更新失败")
    })
    @Log(title = "更新个人简介", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult updateProfile(@ApiParam(value = "个人简介信息", required = true) @RequestBody AppUserProfile appUserProfile)
    {
        Long userId = getUserId();
        
        // 验证是否为当前用户的简介
        AppUserProfile existProfile = appUserProfileService.selectAppUserProfileByUserId(userId);
        if (existProfile == null) {
            return error("个人简介不存在");
        }
        
        // 只允许更新特定字段
        existProfile.setProfileDesc(appUserProfile.getProfileDesc());
        existProfile.setProfileImages(appUserProfile.getProfileImages());
        existProfile.setProfileVideo(appUserProfile.getProfileVideo());
        
        int result = appUserProfileService.updateAppUserProfile(existProfile);
        return result > 0 ? success("更新成功") : error("更新失败");
    }

    /**
     * 上传个人简介图片
     * 支持上传多张图片用于个人介绍
     *
     * @param file 图片文件
     * @return 上传结果和图片路径
     */
    @ApiOperation(value = "上传个人简介图片",
                  notes = "上传个人简介图片，支持jpg、png、gif格式")
    @ApiResponses({
        @ApiResponse(code = 200, message = "上传成功，返回图片路径"),
        @ApiResponse(code = 500, message = "上传失败")
    })
    @Log(title = "上传个人简介图片", businessType = BusinessType.INSERT)
    @PostMapping("/uploadImage")
    public AjaxResult uploadImage(@ApiParam(value = "图片文件", required = true) @RequestParam("file") MultipartFile file)
    {
        try {
            if (file.isEmpty()) {
                return error("上传文件不能为空");
            }

            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = "/profile" + fileName;
            
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("fileName", fileName);
            result.put("originalName", file.getOriginalFilename());
            
            return success("上传成功", result);
        } catch (Exception e) {
            return error("上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传个人简介视频
     * 支持上传短视频用于个人介绍
     *
     * @param file 视频文件
     * @return 上传结果和视频路径
     */
    @ApiOperation(value = "上传个人简介视频",
                  notes = "上传个人简介短视频，支持mp4、avi、mov格式")
    @ApiResponses({
        @ApiResponse(code = 200, message = "上传成功，返回视频路径"),
        @ApiResponse(code = 500, message = "上传失败")
    })
    @Log(title = "上传个人简介视频", businessType = BusinessType.INSERT)
    @PostMapping("/uploadVideo")
    public AjaxResult uploadVideo(@ApiParam(value = "视频文件", required = true) @RequestParam("file") MultipartFile file)
    {
        try {
            if (file.isEmpty()) {
                return error("上传文件不能为空");
            }

            // 检查文件大小（限制50MB）
            if (file.getSize() > 50 * 1024 * 1024) {
                return error("视频文件大小不能超过50MB");
            }

            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = "/profile" + fileName;
            
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("fileName", fileName);
            result.put("originalName", file.getOriginalFilename());
            
            return success("上传成功", result);
        } catch (Exception e) {
            return error("上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户履历时间线
     * 获取用户的重要事件记录
     *
     * @return 履历时间线列表
     */
    @ApiOperation(value = "获取履历时间线",
                  notes = "获取当前用户的履历时间线，包括注册、完成任务、获得奖励等事件")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回履历时间线列表")
    })
    @GetMapping("/timeline")
    public AjaxResult getTimeline(@ApiParam(value = "用户主键，为空则查询当前登录人", required = false)@RequestParam(value = "userId",required = false)Long userId)
    {
        if(StringUtils.isNull(userId)){
            userId = getUserId();
        }
        List<AppUserTimeline> timeline = appUserTimelineService.selectAppUserTimelineByUserId(userId);
        return success(timeline);
    }

}
