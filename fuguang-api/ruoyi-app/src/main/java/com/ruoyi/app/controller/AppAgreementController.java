package com.ruoyi.app.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.service.IAppConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP协议接口控制器
 * 提供用户协议、隐私政策等法律文档的获取功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP协议接口", description = "APP协议和法律文档相关接口")
@RestController("appAgreementController")
@RequestMapping("/app/agreement")
public class AppAgreementController extends BaseController
{
    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 获取用户协议
     * 获取平台用户服务协议内容
     *
     * @return 用户协议内容
     */
    @Anonymous
    @ApiOperation(value = "获取用户协议",
                  notes = "获取平台用户服务协议的完整内容，用于用户注册时阅读和同意")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回协议内容"),
        @ApiResponse(code = 500, message = "获取失败或协议未配置")
    })
    @GetMapping("/user")
    public AjaxResult getUserAgreement()
    {
        try {
            String userAgreement = appConfigService.getUserAgreement();
            if (userAgreement == null || userAgreement.isEmpty()) {
                return success("用户协议内容暂未配置，请联系管理员在后台配置协议内容。");
            }
            return success("请求成功",userAgreement);
        } catch (Exception e) {
            return error("获取用户协议失败：" + e.getMessage());
        }
    }

    /**
     * 获取隐私政策
     * 获取平台隐私保护政策内容
     *
     * @return 隐私政策内容
     */
    @Anonymous
    @ApiOperation(value = "获取隐私协议",
                  notes = "获取平台隐私保护政策的完整内容，说明用户信息收集和使用规则")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回隐私政策内容"),
        @ApiResponse(code = 500, message = "获取失败或政策未配置")
    })
    @GetMapping("/privacy")
    public AjaxResult getPrivacyPolicy()
    {
        try {
            String privacyPolicy = appConfigService.getPrivacyPolicy();
            if (privacyPolicy == null || privacyPolicy.isEmpty()) {
                return success("隐私协议内容暂未配置，请联系管理员在后台配置协议内容。");
            }
            return success("请求成功",privacyPolicy);
        } catch (Exception e) {
            return error("获取隐私协议失败：" + e.getMessage());
        }
    }
}
