package com.ruoyi.app.controller;

import java.util.List;
import com.ruoyi.common.annotation.Anonymous;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.domain.AppFunction;
import com.ruoyi.fuguang.service.IAppUserService;
import com.ruoyi.fuguang.service.IAppFunctionService;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.fuguang.domain.MerchantApplication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import java.util.HashMap;
import java.util.Map;

/**
 * APP用户个人信息控制器
 * 提供用户个人信息管理、头像上传、实名认证等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP用户个人信息", description = "APP用户个人信息管理相关接口")
@RestController("appUserApiController")
@RequestMapping("/app/user")
public class AppUserController extends BaseController
{
    @Autowired
    private IAppUserService appUserService;

    @Autowired
    private IAppFunctionService appFunctionService;

    @Autowired
    private ServerConfig serverConfig;

    /**
     * 获取当前用户个人信息
     * 包含用户基本信息和商家申请状态（如果是普通用户）
     *
     * @return 用户个人信息和相关状态
     */
    @ApiOperation(value = "获取个人信息",
                  notes = "获取当前登录用户的个人信息，包括基本信息、商家申请状态等")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回用户信息"),
        @ApiResponse(code = 500, message = "用户信息不存在")
    })
    @GetMapping("/profile")
    public AjaxResult profile()
    {
        Long userId = getUserId();
        AppUser user = appUserService.selectAppUserByUserId(userId);
        if (StringUtils.isNotNull(user))
        {
            return success(user);
        }
        return error("用户信息不存在");
    }

    /**
     * 修改用户
     */
    @ApiOperation("修改个人信息")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/profile")
    public AjaxResult updateProfile(@RequestBody AppUser user)
    {
        Long userId = getUserId();
        user.setUserId(userId);
        
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !appUserService.checkPhoneUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !appUserService.checkEmailUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        
        if (appUserService.updateUserProfile(user) > 0)
        {
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @ApiOperation("重置密码")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword)
    {
        Long userId = getUserId();
        AppUser user = appUserService.selectAppUserByUserId(userId);
        String userName = user.getUserName();
        String password = user.getPassword();
        
        if (!SecurityUtils.matchesPassword(oldPassword, password))
        {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password))
        {
            return error("新密码不能与旧密码相同");
        }
        
        if (appUserService.resetUserPwd(userName, newPassword) > 0)
        {
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 用户头像上传
     * 支持jpg、png、gif格式，文件大小不超过2MB
     *
     * @param file 头像文件
     * @return 上传结果，包含图片URL
     */
    @ApiOperation(value = "头像上传",
                  notes = "上传用户头像，支持jpg、png、gif格式，文件大小不超过2MB")
    @ApiResponses({
        @ApiResponse(code = 200, message = "上传成功，返回图片URL"),
        @ApiResponse(code = 500, message = "上传失败，文件格式不支持或文件过大")
    })
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@ApiParam(value = "头像文件", required = true) @RequestParam("avatarfile") MultipartFile file) throws Exception
    {
        if (!file.isEmpty())
        {
            String avatar = FileUploadUtils.upload(RuoYiConfig.getAvatarPath(), file);
            if (appUserService.updateUserAvatar(getUserId(), avatar))
            {
                AjaxResult ajax = AjaxResult.success();
                // 返回完整的图片URL，包含服务器地址
                String fullUrl = serverConfig.getUrl() + avatar;
                ajax.put("imgUrl", fullUrl);
                return ajax;
            }
        }
        return error("上传图片异常，请联系管理员");
    }

    /**
     * 用户实名认证
     * 提交真实姓名和身份证号进行实名认证
     *
     * @param user 包含真实姓名和身份证号的用户信息
     * @return 认证结果
     */
    @ApiOperation(value = "实名认证",
                  notes = "提交真实姓名和身份证号进行实名认证，认证通过后可使用更多功能")
    @ApiResponses({
        @ApiResponse(code = 200, message = "实名认证成功"),
        @ApiResponse(code = 500, message = "认证失败，信息不完整或身份验证失败")
    })
    @Log(title = "实名认证", businessType = BusinessType.UPDATE)
    @PostMapping("/auth")
    public AjaxResult realNameAuth(@ApiParam(value = "用户认证信息，realName和idCard必填", required = true) @RequestBody AppUser user)
    {
        Long userId = getUserId();

        if (StringUtils.isEmpty(user.getRealName()) || StringUtils.isEmpty(user.getIdCard()))
        {
            return error("真实姓名和身份证号不能为空");
        }

        // TODO: 调用第三方实名认证接口验证身份信息

        AppUser updateUser = new AppUser();
        updateUser.setUserId(userId);
        updateUser.setRealName(user.getRealName());
        updateUser.setIdCard(user.getIdCard());
        updateUser.setAuthStatus("1"); // 设置为已认证

        if (appUserService.updateUserProfile(updateUser) > 0)
        {
            return success("实名认证成功");
        }
        return error("实名认证失败，请联系管理员");
    }

    /**
     * 获取用户分数信息
     */
    @ApiOperation("获取用户分数信息")
    @GetMapping("/scores")
    public AjaxResult getUserScores()
    {
        Long userId = getUserId();
        // TODO: 从数据库获取用户分数信息
        Map<String, Object> scores = new HashMap<>();
        scores.put("trustScore", 85); // 信任分
        scores.put("executeScore", 92); // 执行分
        return success(scores);
    }
    /**
     * 获取用户财务信息
     */
    @ApiOperation("获取用户财务信息")
    @GetMapping("/finance")
    public AjaxResult getUserFinance()
    {
        Long userId = getUserId();
        // TODO: 从数据库获取用户财务信息
        Map<String, Object> finance = new HashMap<>();
        finance.put("deposit", 500.00); // 保障金
        finance.put("commission", 128.50); // 佣金
        return success(finance);
    }
    /**
     * 获取用户中心功能配置
     */
    @Anonymous
    @ApiOperation("获取用户中心功能配置")
    @GetMapping("/functions")
    public AjaxResult getUserCenterFunctions()
    {
        try {
            // 获取我的页面显示的功能列表（displayLocation = '1'）
            List<AppFunction> functions = appFunctionService.selectEnabledAppFunctionListByLocation("1");
            return success(functions);
        } catch (Exception e) {
            return error("获取用户中心功能配置失败：" + e.getMessage());
        }
    }
}
