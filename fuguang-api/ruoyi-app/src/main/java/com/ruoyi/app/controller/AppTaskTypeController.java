package com.ruoyi.app.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppTaskType;
import com.ruoyi.fuguang.service.IAppTaskTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP任务类型管理控制器
 * 提供任务分类的查询功能，支持树形结构
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Api(tags = "APP任务类型接口", description = "APP任务分类相关接口")
@RestController("appTaskTypeApiController")
@RequestMapping("/app/taskType")
public class AppTaskTypeController extends BaseController
{
    @Autowired
    private IAppTaskTypeService appTaskTypeService;

    /**
     * 获取一级任务类型列表
     * 获取所有顶级任务分类，用于首页分类展示
     *
     * @return 一级任务类型列表
     */
    @ApiOperation(value = "获取一级任务类型列表",
                  notes = "获取所有顶级任务分类，用于首页分类展示和筛选")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回一级分类列表")
    })
    @GetMapping("/firstLevel")
    public AjaxResult getFirstLevelTypes()
    {
        List<AppTaskType> list = appTaskTypeService.selectFirstLevelTaskTypes();
        return success(list);
    }

    /**
     * 根据父类型ID获取子类型列表
     * 获取指定父分类下的所有子分类
     *
     * @param parentId 父类型ID
     * @return 子类型列表
     */
    @ApiOperation(value = "根据父类型ID获取子类型列表",
                  notes = "获取指定父分类下的所有子分类，用于分类树形展示")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回子分类列表")
    })
    @GetMapping("/children/{parentId}")
    public AjaxResult getChildrenTypes(@ApiParam(value = "父类型ID", required = true) @PathVariable("parentId") Long parentId)
    {
        List<AppTaskType> list = appTaskTypeService.selectTaskTypesByParentId(parentId);
        return success(list);
    }
    /**
     * 获取任务类型树形结构
     */
    @ApiOperation("获取任务类型树形结构")
    @GetMapping("/tree")
    public AjaxResult getTaskTypeTree()
    {
        List<AppTaskType> list = appTaskTypeService.buildTaskTypeTree();
        return success(list);
    }
}
