package com.ruoyi.app.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.fuguang.domain.AppActivity;
import com.ruoyi.fuguang.service.IAppActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP活动接口控制器
 * 提供活动查询等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP活动接口", description = "APP活动管理相关接口")
@RestController("appActivityApiController")
@RequestMapping("/app/activity")
public class AppActivityController extends BaseController
{
    @Autowired
    private IAppActivityService appActivityService;

    /**
     * 查询活动列表
     * 获取所有启用状态的活动列表
     *
     * @return 活动列表
     */
    @Anonymous
    @ApiOperation(value = "查询活动列表",
                  notes = "获取所有启用状态的活动列表，按排序和创建时间排序")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回活动列表")
    })
    @GetMapping("/list")
    public TableDataInfo list()
    {
        startPage();
        List<AppActivity> list = appActivityService.selectValidAppActivityList();
        return getDataTable(list);
    }

    /**
     * 获取活动详情
     * 根据活动ID获取活动的详细信息
     *
     * @param activityId 活动ID
     * @return 活动详情
     */
    @Anonymous
    @ApiOperation(value = "获取活动详情",
                  notes = "根据活动ID获取活动的详细信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回活动详情"),
        @ApiResponse(code = 404, message = "活动不存在")
    })
    @GetMapping("/{activityId}")
    public AjaxResult getInfo(@ApiParam(value = "活动ID", required = true) @PathVariable("activityId") Long activityId)
    {
        AppActivity activity = appActivityService.selectAppActivityByActivityId(activityId);
        if (activity == null) {
            return error("活动不存在");
        }
        return success(activity);
    }


}
