package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.fuguang.domain.MallCategory;
import com.ruoyi.fuguang.domain.MallProduct;
import com.ruoyi.fuguang.domain.MallProductSpec;
import com.ruoyi.fuguang.service.IMallCategoryService;
import com.ruoyi.fuguang.service.IMallProductService;
import com.ruoyi.fuguang.service.IMallProductSpecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP商城接口控制器
 * 提供商城首页、商品分类、商品列表、商品详情等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP商城接口", description = "APP商城相关接口")
@RestController("appMallController")
@RequestMapping("/app/mall")
public class AppMallController extends BaseController
{
    @Autowired
    private IMallCategoryService mallCategoryService;

    @Autowired
    private IMallProductService mallProductService;

    @Autowired
    private IMallProductSpecService mallProductSpecService;

    /**
     * 获取商城首页数据
     * 包含商品分类、热门商品、新品推荐、推荐商品等信息
     *
     * @return 商城首页数据
     */
    @Anonymous
    @ApiOperation(value = "获取商城首页数据",
                  notes = "获取商城首页展示数据，包括一级分类列表、热门商品、新品推荐、推荐商品等")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回首页数据")
    })
    @GetMapping("/home")
    public AjaxResult getHomeData()
    {
        Map<String, Object> data = new HashMap<>();

        // 获取一级分类列表
        List<MallCategory> categories = mallCategoryService.selectTopCategoryList();
        data.put("categories", categories);

        // 获取热门商品（前8个）
        List<MallProduct> hotProducts = mallProductService.selectHotProductList(8);
        data.put("hotProducts", hotProducts);

        // 获取新品推荐（前8个）
        List<MallProduct> newProducts = mallProductService.selectNewProductList(8);
        data.put("newProducts", newProducts);

        // 获取推荐商品（前8个）
        List<MallProduct> recommendProducts = mallProductService.selectRecommendProductList(8);
        data.put("recommendProducts", recommendProducts);

        return success(data);
    }

    /**
     * 获取商品分类列表
     * 返回树形结构的商品分类列表，包含所有启用的分类
     *
     * @return 商品分类树形列表
     */
    @Anonymous
    @ApiOperation(value = "获取商品分类列表",
                  notes = "获取树形结构的商品分类列表，包含所有启用的分类及其子分类")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回分类树形列表")
    })
    @GetMapping("/categories")
    public AjaxResult getCategoryList()
    {
        List<MallCategory> list = mallCategoryService.selectEnabledCategoryTree();
        return success(list);
    }

    /**
     * 获取商品列表
     * 支持按分类筛选和关键词搜索，支持分页
     *
     * @param categoryId 分类ID，可选
     * @param keyword 搜索关键词，可选
     * @return 商品列表
     */
    @Anonymous
    @ApiOperation(value = "获取商品列表",
                  notes = "获取商品列表，支持按分类筛选和关键词搜索，支持分页")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回商品列表")
    })
    @GetMapping("/products")
    public TableDataInfo getProductList(
            @ApiParam(value = "分类ID", required = false) @RequestParam(required = false) Long categoryId,
            @ApiParam(value = "搜索关键词", required = false) @RequestParam(required = false) String keyword)
    {
        startPage();
        List<MallProduct> list;

        if (keyword != null && !keyword.trim().isEmpty()) {
            // 搜索商品
            list = mallProductService.searchProducts(keyword.trim());
        } else if (categoryId != null) {
            // 按分类查询
            list = mallProductService.selectProductListByCategoryId(categoryId, null);
        } else {
            // 查询所有商品
            MallProduct mallProduct = new MallProduct();
            mallProduct.setProductStatus("0"); // 只查询上架商品
            list = mallProductService.selectMallProductList(mallProduct);
        }
        
        return getDataTable(list);
    }

    /**
     * 获取商品详情
     */
    @Anonymous
    @ApiOperation("获取商品详情")
    @GetMapping("/product/{productId}")
    public AjaxResult getProductDetail(@ApiParam("商品ID") @PathVariable Long productId)
    {
        MallProduct product = mallProductService.selectMallProductByProductId(productId);
        if (product == null) {
            return error("商品不存在");
        }
        
        if (!"0".equals(product.getProductStatus())) {
            return error("商品已下架");
        }
        
        return success(product);
    }

    /**
     * 获取商品规格列表
     */
    @Anonymous
    @ApiOperation("获取商品规格列表")
    @GetMapping("/product/{productId}/specs")
    public AjaxResult getProductSpecs(@ApiParam("商品ID") @PathVariable Long productId)
    {
        // 先验证商品是否存在且已上架
        MallProduct product = mallProductService.selectMallProductByProductId(productId);
        if (product == null) {
            return error("商品不存在");
        }

        if (!"0".equals(product.getProductStatus())) {
            return error("商品已下架");
        }

        // 查询商品规格列表
        MallProductSpec querySpec = new MallProductSpec();
        querySpec.setProductId(productId);
        querySpec.setSpecStatus("0"); // 只查询上架的规格
        List<MallProductSpec> specs = mallProductSpecService.selectMallProductSpecList(querySpec);

        return success(specs);
    }



    /**
     * 获取热门商品
     */
    @Anonymous
    @ApiOperation("获取热门商品")
    @GetMapping("/hot")
    public AjaxResult getHotProducts(@ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit)
    {
        List<MallProduct> list = mallProductService.selectHotProductList(limit);
        return success(list);
    }

    /**
     * 获取新品推荐
     */
    @Anonymous
    @ApiOperation("获取新品推荐")
    @GetMapping("/new")
    public AjaxResult getNewProducts(@ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit)
    {
        List<MallProduct> list = mallProductService.selectNewProductList(limit);
        return success(list);
    }

    /**
     * 获取推荐商品
     */
    @Anonymous
    @ApiOperation("获取推荐商品")
    @GetMapping("/recommend")
    public AjaxResult getRecommendProducts(@ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit)
    {
        List<MallProduct> list = mallProductService.selectRecommendProductList(limit);
        return success(list);
    }
}
