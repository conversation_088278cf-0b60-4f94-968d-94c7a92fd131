package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.domain.TaskEvaluation;
import com.ruoyi.fuguang.service.ITaskEvaluationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP任务评价接口控制器
 * 提供任务评价提交、查询等功能
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Api(tags = "APP任务评价接口", description = "APP任务评价相关接口")
@RestController("appTaskEvaluationController")
@RequestMapping("/app/evaluation")
public class AppTaskEvaluationController extends BaseController
{
    @Autowired
    private ITaskEvaluationService taskEvaluationService;

    /**
     * 提交任务评价
     * 发单人对接单人进行评价
     *
     * @param evaluationData 评价数据
     * @return 提交结果
     */
    @ApiOperation(value = "提交任务评价",
                  notes = "发单人对已完成任务的接单人进行评价，评分1-5分")
    @ApiResponses({
        @ApiResponse(code = 200, message = "评价提交成功"),
        @ApiResponse(code = 400, message = "评价提交失败，参数错误或任务状态不符合")
    })
    @PostMapping("/submit")
    public AjaxResult submitEvaluation(@RequestBody Map<String, Object> evaluationData)
    {
        try {
            Long taskId = Long.valueOf(evaluationData.get("taskId").toString());
            Long publisherId = getUserId();
            Integer rating = Integer.valueOf(evaluationData.get("rating").toString());
            String evaluationContent = (String) evaluationData.get("evaluationContent");
            String evaluationTags = (String) evaluationData.get("evaluationTags");
            String isAnonymous = (String) evaluationData.get("isAnonymous");

            // 验证评分范围
            if (rating < 1 || rating > 5) {
                return error("评分必须在1-5分之间");
            }
            boolean result = taskEvaluationService.submitTaskEvaluation(
                taskId, publisherId, rating, evaluationContent, evaluationTags, isAnonymous);
            if (result) {
                return success("评价提交成功");
            } else {
                return error("评价提交失败，请检查任务状态或是否已评价过");
            }
        } catch (Exception e) {
            logger.error("提交任务评价失败", e);
            return error("评价提交失败：" + e.getMessage());
        }
    }



    /**
     * 根据任务ID查询评价
     * 获取指定任务的评价信息
     *
     * @param taskId 任务ID
     * @return 任务评价信息
     */
    @ApiOperation(value = "根据任务ID查询评价",
                  notes = "获取指定任务的评价信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回评价信息")
    })
    @GetMapping("/task/{taskId}")
    public AjaxResult getByTaskId(@ApiParam(value = "任务ID", required = true) @PathVariable("taskId") Long taskId)
    {
        TaskEvaluation evaluation = taskEvaluationService.selectTaskEvaluationByTaskId(taskId);
        return success(evaluation);
    }

    /**
     * 根据接单人ID查询评价列表
     * 获取指定用户收到的所有评价
     *
     * @param receiverId 接单人ID
     * @return 评价列表
     */
    @ApiOperation(value = "根据接单人ID查询评价列表",
                  notes = "获取指定用户收到的所有评价列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回评价列表")
    })
    @GetMapping("/receiver/{receiverId}")
    public AjaxResult getByReceiverId(@ApiParam(value = "接单人ID", required = true) @PathVariable("receiverId") Long receiverId)
    {
        List<TaskEvaluation> list = taskEvaluationService.selectTaskEvaluationByReceiverId(receiverId);
        return success(list);
    }

    /**
     * 根据发单人ID查询评价列表
     * 获取指定用户发出的所有评价
     *
     * @param publisherId 发单人ID
     * @return 评价列表
     */
    @ApiOperation(value = "根据发单人ID查询评价列表",
                  notes = "获取指定用户发出的所有评价列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回评价列表")
    })
    @GetMapping("/publisher/{publisherId}")
    public AjaxResult getByPublisherId(@ApiParam(value = "发单人ID", required = true) @PathVariable("publisherId") Long publisherId)
    {
        List<TaskEvaluation> list = taskEvaluationService.selectTaskEvaluationByPublisherId(publisherId);
        return success(list);
    }

    /**
     * 获取当前用户发出的评价列表
     * 获取当前登录用户发出的所有评价
     *
     * @return 评价列表
     */
    @ApiOperation(value = "获取当前用户发出的评价列表",
                  notes = "获取当前登录用户发出的所有评价列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回评价列表")
    })
    @GetMapping("/myEvaluations")
    public AjaxResult getMyEvaluations()
    {
        Long publisherId = getUserId();
        List<TaskEvaluation> list = taskEvaluationService.selectTaskEvaluationByPublisherId(publisherId);
        return success(list);
    }

    /**
     * 获取当前用户收到的评价列表
     * 获取当前登录用户收到的所有评价
     *
     * @return 评价列表
     */
    @ApiOperation(value = "获取当前用户收到的评价列表",
                  notes = "获取当前登录用户收到的所有评价列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回评价列表")
    })
    @GetMapping("/receivedEvaluations")
    public AjaxResult getReceivedEvaluations()
    {
        Long receiverId = getUserId();
        List<TaskEvaluation> list = taskEvaluationService.selectTaskEvaluationByReceiverId(receiverId);
        return success(list);
    }

    /**
     * 获取用户评价统计信息
     * 获取指定用户的评价统计数据
     *
     * @param receiverId 接单人ID
     * @return 评价统计信息
     */
    @ApiOperation(value = "获取用户评价统计信息",
                  notes = "获取指定用户的评价统计数据，包括平均评分、评价总数、各评分等级统计等")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回统计信息")
    })
    @GetMapping("/statistics/{receiverId}")
    public AjaxResult getStatistics(@ApiParam(value = "接单人ID", required = true) @PathVariable("receiverId") Long receiverId)
    {
        Map<String, Object> statistics = taskEvaluationService.getEvaluationStatistics(receiverId);
        return success(statistics);
    }

    /**
     * 获取当前用户的评价统计信息
     * 获取当前登录用户的评价统计数据
     *
     * @return 评价统计信息
     */
    @ApiOperation(value = "获取当前用户的评价统计信息",
                  notes = "获取当前登录用户的评价统计数据")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回统计信息")
    })
    @GetMapping("/myStatistics")
    public AjaxResult getMyStatistics()
    {
        Long receiverId = getUserId();
        Map<String, Object> statistics = taskEvaluationService.getEvaluationStatistics(receiverId);
        return success(statistics);
    }
}
